package com.flopotech.bg.quote.oz;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import java.util.Collection;
import java.util.Map;
import java.util.UUID;

import com.flopotech.bg.quote.oz.service.SubscriptionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.util.ReflectionTestUtils;

import quickfix.FieldNotFound;
import quickfix.Session;
import quickfix.SessionID;
import quickfix.SessionNotFound;
import quickfix.field.*;
import quickfix.fix44.MarketDataRequest;
import quickfix.fix44.MarketDataRequestReject;
import quickfix.fix44.MarketDataSnapshotFullRefresh;

@SpringBootTest(classes = {SubscriptionService.class})
class SubscriptionServiceTest {

    @Autowired
    private SubscriptionService subscriptionService;


    private final String symbol = "AAPL";
    private final String mdReqId = "MD_REQ_12345";

    private final SessionID sessionID = new SessionID("FIX.4.4",  // FIX版本
            "MT4-1",
            "UAT_Q",
            "EBC_UAT_Q",   // 可选参数，可为null
            String.valueOf(true));

    @Test
    void testHandleSubscriptionReject() throws FieldNotFound {
        // 准备测试数据
        MarketDataRequestReject reject = new MarketDataRequestReject();
        reject.set(new MDReqID(mdReqId));
        reject.set(new MDReqRejReason(MDReqRejReason.DUPLICATE_MDREQID));

        // 设置pending状态
        SubscriptionService.SymbolSubConfig config = new SubscriptionService.SymbolSubConfig(sessionID, symbol);
        config.setMdReqID(mdReqId);
        getPendingMap().put(symbol, config);

        // 执行
        subscriptionService.handleSubscriptionReject(reject);

        // 验证状态转移
        assertFalse(getPendingMap().containsKey(symbol));
        assertTrue(getSubscriptionFailedMap().containsKey(symbol));
        assertEquals(0, getErrorFailedMap().size());
    }


    @Test
    void testHandleSubscriptionRejectWithPermanentError() throws FieldNotFound {
        // 准备测试数据
        MarketDataRequestReject reject = new MarketDataRequestReject();
        reject.set(new MDReqID(mdReqId));
        reject.set(new MDReqRejReason(MDReqRejReason.UNKNOWN_SYMBOL)); // 永久错误

        // 设置pending状态
        SubscriptionService.SymbolSubConfig config = new SubscriptionService.SymbolSubConfig(sessionID, symbol);
        config.setMdReqID(mdReqId);
        getPendingMap().put(symbol, config);

        // 执行
        subscriptionService.handleSubscriptionReject(reject);

        // 验证状态转移
        assertFalse(getPendingMap().containsKey(symbol));
        assertTrue(getErrorFailedMap().containsKey(symbol));
    }

    @Test
    void testHandleSubscriptionSuccess() throws FieldNotFound {
        // 准备测试数据
        MarketDataSnapshotFullRefresh refresh = new MarketDataSnapshotFullRefresh();
        refresh.set(new MDReqID(mdReqId));

        // 设置pending状态
        SubscriptionService.SymbolSubConfig config = new SubscriptionService.SymbolSubConfig(sessionID, symbol);
        config.setMdReqID(mdReqId);
        getPendingMap().put(symbol, config);

        // 执行
        subscriptionService.handleSubscriptionSuccess(refresh);

        // 验证状态转移
        assertFalse(getPendingMap().containsKey(symbol));
        assertTrue(getSuccessfulMap().containsKey(symbol));
        assertEquals(0, getSubscriptionFailedMap().size());
    }

    @Test
    void testGetSubConfig() {
        // 准备不同状态
        SubscriptionService.SymbolSubConfig pendingConfig = new SubscriptionService.SymbolSubConfig(sessionID, symbol);
        getPendingMap().put(symbol, pendingConfig);

        // 执行查询
        SubscriptionService.SymbolSubConfig result = subscriptionService.getSubConfig(sessionID, symbol);

        // 验证状态标记
        assertNotNull(result);
        assertEquals("PENDING", result.getStatus());
    }

    @Test
    void testGetSubFlag() {
        // 准备存在状态
        getSuccessfulMap().put(symbol, new SubscriptionService.SymbolSubConfig(sessionID, symbol));

        // 验证存在状态
        assertTrue(subscriptionService.getSubFlag(sessionID, symbol));
        assertFalse(subscriptionService.getSubFlag(sessionID, "UNKNOWN"));
    }


    // 辅助方法：获取内部状态Map
    @SuppressWarnings("unchecked")
    private Map<String, SubscriptionService.SymbolSubConfig> getPendingMap() {
        return (Map<String, SubscriptionService.SymbolSubConfig>)
                ReflectionTestUtils.getField(subscriptionService, "pendingSubscriptions");
    }

    @SuppressWarnings("unchecked")
    private Map<String, SubscriptionService.SymbolSubConfig> getRequestFailedMap() {
        return (Map<String, SubscriptionService.SymbolSubConfig>)
                ReflectionTestUtils.getField(subscriptionService, "requestFailedSubscriptions");
    }

    @SuppressWarnings("unchecked")
    private Map<String, SubscriptionService.SymbolSubConfig> getSubscriptionFailedMap() {
        return (Map<String, SubscriptionService.SymbolSubConfig>)
                ReflectionTestUtils.getField(subscriptionService, "subscriptionFailedSymbols");
    }

    @SuppressWarnings("unchecked")
    private Map<String, SubscriptionService.SymbolSubConfig> getSuccessfulMap() {
        return (Map<String, SubscriptionService.SymbolSubConfig>)
                ReflectionTestUtils.getField(subscriptionService, "successfulSubscriptions");
    }

    @SuppressWarnings("unchecked")
    private Map<String, SubscriptionService.SymbolSubConfig> getErrorFailedMap() {
        return (Map<String, SubscriptionService.SymbolSubConfig>)
                ReflectionTestUtils.getField(subscriptionService, "errorFailedSymbols");
    }
}
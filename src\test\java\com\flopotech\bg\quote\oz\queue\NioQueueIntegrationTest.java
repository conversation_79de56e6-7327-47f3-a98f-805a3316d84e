package com.flopotech.bg.quote.oz.queue;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import com.flopotech.bg.quote.oz.service.MarketDataProducer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * NIO双队列系统集成测试
 * 测试双队列架构：rawQueue -> sortedQueue -> Kafka
 */
class NioQueueIntegrationTest {

    private SymbolBasedMarketDataQueue queue;
    private TestMarketDataProducer testProducer;

    @BeforeEach
    void setUp() {
        queue = new SymbolBasedMarketDataQueue();
        testProducer = new TestMarketDataProducer();

        // 注入测试用的producer
        ReflectionTestUtils.setField(queue, "marketDataProducer", testProducer);

        // 设置测试用的symbol配置
        ReflectionTestUtils.setField(queue, "subscribeSymbols", "GBPUSD,EURUSD,USDJPY");

        // 初始化队列
        queue.initialize();
    }

    /**
     * 测试用的MarketDataProducer实现
     */
    private static class TestMarketDataProducer {
        private int sendCount = 0;
        private int sendWithBufferCount = 0;

        public void sendMarketData(MarketDataMsg.MarketData marketData) {
            sendCount++;
            System.out.println("发送市场数据: " + marketData.getSymbol() + " @ " + marketData.getTimestamp());
        }

        public void sendMarketData(MarketDataMsg.MarketData marketData, java.nio.ByteBuffer buffer) {
            sendWithBufferCount++;
            System.out.println("发送市场数据(ByteBuffer): " + marketData.getSymbol() + " @ " + marketData.getTimestamp()
                    + ", buffer size: " + buffer.remaining());
        }

        public int getSendCount() {
            return sendCount;
        }

        public int getSendWithBufferCount() {
            return sendWithBufferCount;
        }

        public int getTotalSendCount() {
            return sendCount + sendWithBufferCount;
        }

        public void resetCount() {
            sendCount = 0;
            sendWithBufferCount = 0;
        }
    }

    @Test
    @DisplayName("测试双队列架构初始化")
    void testDualQueueInitialization() {
        Set<String> configuredSymbols = queue.getConfiguredSymbols();

        assertEquals(3, configuredSymbols.size());
        assertTrue(configuredSymbols.contains("GBPUSD"));
        assertTrue(configuredSymbols.contains("EURUSD"));
        assertTrue(configuredSymbols.contains("USDJPY"));

        // 检查所有队列初始状态为空
        for (String symbol : configuredSymbols) {
            assertTrue(queue.isQueueEmpty(symbol));
        }
    }

    @Test
    @DisplayName("测试NIO零拷贝数据流")
    void testNioZeroCopyDataFlow() throws InterruptedException {
        // 创建测试数据
        MarketDataMsg.MarketData marketData = MarketDataMsg.MarketData.newBuilder()
                .setSymbol("GBPUSD")
                .setTimestamp(System.currentTimeMillis())
                .setBid("1.2500")
                .setOffer("1.2502")
                .build();

        // 测试入队
        boolean result = queue.enqueue(marketData);
        assertTrue(result);

        // 等待一段时间让NIO队列处理数据
        Thread.sleep(500);

        // 验证数据被处理（应该使用ByteBuffer发送）
        assertTrue(testProducer.getTotalSendCount() > 0);

        System.out.println("总发送次数: " + testProducer.getTotalSendCount());
        System.out.println("普通发送次数: " + testProducer.getSendCount());
        System.out.println("ByteBuffer发送次数: " + testProducer.getSendWithBufferCount());
    }

    @Test
    @DisplayName("测试时间戳排序功能")
    void testTimestampSorting() throws InterruptedException {
        long baseTime = System.currentTimeMillis();

        // 创建乱序的时间戳数据
        MarketDataMsg.MarketData[] testData = {
                MarketDataMsg.MarketData.newBuilder()
                        .setSymbol("GBPUSD")
                        .setTimestamp(baseTime + 300) // 最晚
                        .setBid("1.2503")
                        .setOffer("1.2505")
                        .build(),
                MarketDataMsg.MarketData.newBuilder()
                        .setSymbol("GBPUSD")
                        .setTimestamp(baseTime + 100) // 中间
                        .setBid("1.2501")
                        .setOffer("1.2503")
                        .build(),
                MarketDataMsg.MarketData.newBuilder()
                        .setSymbol("GBPUSD")
                        .setTimestamp(baseTime + 50) // 最早
                        .setBid("1.2500")
                        .setOffer("1.2502")
                        .build()
        };

        // 乱序入队
        for (MarketDataMsg.MarketData data : testData) {
            queue.enqueue(data);
        }

        // 等待处理
        Thread.sleep(1000);

        // 验证数据被处理（排序队列会按时间戳排序后发送）
        assertTrue(testProducer.getTotalSendCount() >= 3);

        System.out.println("排序测试 - 总发送次数: " + testProducer.getTotalSendCount());
    }

    @Test
    @DisplayName("测试多symbol并发处理")
    void testMultiSymbolConcurrentProcessing() throws InterruptedException {
        String[] symbols = { "GBPUSD", "EURUSD", "USDJPY" };
        long baseTime = System.currentTimeMillis();

        // 为每个symbol添加数据
        for (int i = 0; i < symbols.length; i++) {
            for (int j = 0; j < 5; j++) {
                MarketDataMsg.MarketData marketData = MarketDataMsg.MarketData.newBuilder()
                        .setSymbol(symbols[i])
                        .setTimestamp(baseTime + j * 100)
                        .setBid("1.000" + j)
                        .setOffer("1.000" + (j + 2))
                        .build();
                queue.enqueue(marketData);
            }
        }

        // 等待处理
        Thread.sleep(1500);

        // 验证所有数据都被处理
        assertTrue(testProducer.getTotalSendCount() >= 15);

        System.out.println("多symbol测试 - 总发送次数: " + testProducer.getTotalSendCount());
    }

    @Test
    @DisplayName("测试未配置symbol的处理")
    void testUnconfiguredSymbolHandling() {
        // 创建未配置symbol的测试数据
        MarketDataMsg.MarketData marketData = MarketDataMsg.MarketData.newBuilder()
                .setSymbol("UNKNOWN")
                .setTimestamp(System.currentTimeMillis())
                .setBid("1.0000")
                .setOffer("1.0002")
                .build();

        // 测试入队
        boolean result = queue.enqueue(marketData);
        assertFalse(result);

        // 检查队列状态
        Map<String, Integer> queueSizes = queue.getQueueSizes();
        assertFalse(queueSizes.containsKey("UNKNOWN"));
    }

    @Test
    @DisplayName("测试队列状态监控")
    void testQueueStatusMonitoring() {
        // 获取队列信息
        String queueInfo = queue.getQueueInfo();

        assertNotNull(queueInfo);
        assertTrue(queueInfo.contains("NIO直接内存队列状态"));
        assertTrue(queueInfo.contains("双队列架构"));
        assertTrue(queueInfo.contains("零拷贝"));
        assertTrue(queueInfo.contains("GBPUSD"));
        assertTrue(queueInfo.contains("EURUSD"));
        assertTrue(queueInfo.contains("USDJPY"));

        System.out.println("队列状态信息:");
        System.out.println(queueInfo);
    }

    @Test
    @DisplayName("测试手动出队功能")
    void testManualDequeue() throws InterruptedException {
        // 先入队一些数据
        MarketDataMsg.MarketData marketData = MarketDataMsg.MarketData.newBuilder()
                .setSymbol("GBPUSD")
                .setTimestamp(System.currentTimeMillis())
                .setBid("1.2500")
                .setOffer("1.2502")
                .build();

        queue.enqueue(marketData);

        // 短暂等待让数据进入队列
        Thread.sleep(100);

        // 尝试手动出队
        MarketDataMsg.MarketData dequeued = queue.dequeue("GBPUSD");

        // 注意：由于NIO队列的自动处理，数据可能已经被自动发送了
        // 所以这里主要测试方法不会抛异常
        assertDoesNotThrow(() -> queue.dequeue("GBPUSD"));
    }

    @Test
    @DisplayName("测试异常情况处理")
    void testExceptionHandling() {
        // 测试null数据
        assertFalse(queue.enqueue(null));

        // 测试空symbol
        MarketDataMsg.MarketData emptySymbol = MarketDataMsg.MarketData.newBuilder()
                .setSymbol("")
                .setTimestamp(System.currentTimeMillis())
                .setBid("1.0000")
                .setOffer("1.0002")
                .build();
        assertFalse(queue.enqueue(emptySymbol));

        // 测试未配置symbol的出队
        assertNull(queue.dequeue("UNKNOWN"));
    }
}

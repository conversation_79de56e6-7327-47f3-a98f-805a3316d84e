package com.flopotech.bg.quote.oz.queue;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import com.flopotech.bg.quote.oz.service.MarketDataProducer;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.*;

/**
 * 基于配置文件symbol的市场数据队列服务
 * 根据quickfix.onezero.subscribe.symbol配置进行分片处理
 */
@Component
public class SymbolBasedMarketDataQueue {

    private static final Logger logger = LoggerFactory.getLogger(SymbolBasedMarketDataQueue.class);

    @Autowired
    private MarketDataProducer marketDataProducer;

    /**
     * 从配置文件读取订阅的symbol列表
     */
    @Value("${quickfix.onezero.subscribe.symbol}")
    private String subscribeSymbols;

    /**
     * 基于symbol的分片队列
     * 每个symbol对应一个独立的优先级队列（按时间戳排序）
     */
    private final ConcurrentMap<String, PriorityBlockingQueue<MarketDataMsg.MarketData>> symbolQueues = new ConcurrentHashMap<>();

    /**
     * 队列处理线程池
     */
    private ExecutorService queueProcessorExecutor;

    /**
     * 配置的symbol列表
     */
    private Set<String> configuredSymbols;

    /**
     * 队列运行状态
     */
    private volatile boolean running = true;

    /**
     * 市场数据比较器 - 按时间戳正序排序（最早的在前）
     */
    private static final Comparator<MarketDataMsg.MarketData> TIMESTAMP_COMPARATOR = 
            Comparator.comparing(MarketDataMsg.MarketData::getTimestamp);

    @PostConstruct
    public void initialize() {
        // 解析配置的symbol列表
        parseConfiguredSymbols();
        
        // 初始化每个symbol的队列
        initializeSymbolQueues();
        
        // 启动队列处理线程
        startQueueProcessors();
        
        logger.info("SymbolBasedMarketDataQueue 初始化完成，配置的symbols: {}", configuredSymbols);
    }

    /**
     * 解析配置文件中的symbol列表
     */
    private void parseConfiguredSymbols() {
        if (subscribeSymbols == null || subscribeSymbols.trim().isEmpty()) {
            logger.warn("配置文件中未找到quickfix.onezero.subscribe.symbol配置");
            configuredSymbols = new HashSet<>();
            return;
        }

        configuredSymbols = new HashSet<>();
        String[] symbols = subscribeSymbols.split(",");
        for (String symbol : symbols) {
            String trimmedSymbol = symbol.trim();
            if (!trimmedSymbol.isEmpty()) {
                configuredSymbols.add(trimmedSymbol);
            }
        }
        
        logger.info("解析到配置的symbols: {}", configuredSymbols);
    }

    /**
     * 初始化每个symbol的队列
     */
    private void initializeSymbolQueues() {
        for (String symbol : configuredSymbols) {
            PriorityBlockingQueue<MarketDataMsg.MarketData> queue = 
                new PriorityBlockingQueue<>(1000, TIMESTAMP_COMPARATOR);
            symbolQueues.put(symbol, queue);
            logger.debug("为symbol {} 创建队列", symbol);
        }
    }

    /**
     * 启动队列处理线程
     */
    private void startQueueProcessors() {
        int threadCount = Math.max(1, configuredSymbols.size());
        queueProcessorExecutor = Executors.newFixedThreadPool(threadCount, r -> {
            Thread t = new Thread(r, "SymbolQueue-Processor");
            t.setDaemon(true);
            return t;
        });

        // 为每个symbol启动一个处理线程
        for (String symbol : configuredSymbols) {
            queueProcessorExecutor.submit(() -> processSymbolQueue(symbol));
            logger.debug("为symbol {} 启动处理线程", symbol);
        }
    }

    /**
     * 处理特定symbol的队列
     */
    private void processSymbolQueue(String symbol) {
        logger.info("开始处理symbol {} 的队列，线程: {}", symbol, Thread.currentThread().getName());
        
        PriorityBlockingQueue<MarketDataMsg.MarketData> queue = symbolQueues.get(symbol);
        if (queue == null) {
            logger.error("未找到symbol {} 的队列", symbol);
            return;
        }

        while (running) {
            try {
                // 批量处理队列中的数据
                List<MarketDataMsg.MarketData> batch = new ArrayList<>();
                
                // 等待第一个元素
                MarketDataMsg.MarketData firstItem = queue.poll(100, TimeUnit.MILLISECONDS);
                if (firstItem != null) {
                    batch.add(firstItem);
                    
                    // 尝试获取更多元素进行批量处理
                    queue.drainTo(batch, 50); // 最多再取50个
                    
                    // 处理批量数据
                    processBatch(symbol, batch);
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.warn("symbol {} 的队列处理线程被中断", symbol);
                break;
            } catch (Exception e) {
                logger.error("处理symbol {} 队列时发生错误: {}", symbol, e.getMessage(), e);
            }
        }
        
        logger.info("symbol {} 的队列处理线程已停止", symbol);
    }

    /**
     * 处理批量数据
     */
    private void processBatch(String symbol, List<MarketDataMsg.MarketData> batch) {
        if (batch.isEmpty()) {
            return;
        }

        logger.debug("处理symbol {} 的批量数据，数量: {}", symbol, batch.size());

        for (MarketDataMsg.MarketData marketData : batch) {
            try {
                // 发送到Kafka
                marketDataProducer.sendMarketData(marketData);
                
                logger.debug("成功发送市场数据到Kafka - Symbol: {}, Timestamp: {}", 
                    marketData.getSymbol(), marketData.getTimestamp());
                    
            } catch (Exception e) {
                logger.error("发送市场数据到Kafka失败 - Symbol: {}, Timestamp: {}, Error: {}", 
                    marketData.getSymbol(), marketData.getTimestamp(), e.getMessage(), e);
            }
        }
    }

    /**
     * 将市场数据加入队列
     */
    public boolean enqueue(MarketDataMsg.MarketData marketData) {
        if (marketData == null) {
            logger.warn("尝试入队null市场数据");
            return false;
        }

        String symbol = marketData.getSymbol();
        if (symbol == null || symbol.trim().isEmpty()) {
            logger.warn("市场数据缺少symbol信息");
            return false;
        }

        // 检查symbol是否在配置列表中
        if (!configuredSymbols.contains(symbol)) {
            logger.debug("symbol {} 不在配置的订阅列表中，跳过处理", symbol);
            return false;
        }

        PriorityBlockingQueue<MarketDataMsg.MarketData> queue = symbolQueues.get(symbol);
        if (queue == null) {
            logger.error("未找到symbol {} 的队列", symbol);
            return false;
        }

        boolean success = queue.offer(marketData);
        if (success) {
            logger.debug("市场数据入队成功 - Symbol: {}, Timestamp: {}, 队列大小: {}", 
                symbol, marketData.getTimestamp(), queue.size());
                
            // 检查队列积压情况
            if (queue.size() > 500) {
                logger.warn("symbol {} 队列积压严重，当前大小: {}", symbol, queue.size());
            }
        } else {
            logger.error("市场数据入队失败 - Symbol: {}, Timestamp: {}", 
                symbol, marketData.getTimestamp());
        }

        return success;
    }

    /**
     * 获取队列状态信息
     */
    public Map<String, Integer> getQueueSizes() {
        Map<String, Integer> sizes = new HashMap<>();
        for (Map.Entry<String, PriorityBlockingQueue<MarketDataMsg.MarketData>> entry : symbolQueues.entrySet()) {
            sizes.put(entry.getKey(), entry.getValue().size());
        }
        return sizes;
    }

    /**
     * 获取配置的symbol列表
     */
    public Set<String> getConfiguredSymbols() {
        return new HashSet<>(configuredSymbols);
    }

    /**
     * 检查特定symbol的队列是否为空
     */
    public boolean isQueueEmpty(String symbol) {
        PriorityBlockingQueue<MarketDataMsg.MarketData> queue = symbolQueues.get(symbol);
        return queue == null || queue.isEmpty();
    }

    @PreDestroy
    public void shutdown() {
        logger.info("正在关闭SymbolBasedMarketDataQueue...");
        running = false;

        if (queueProcessorExecutor != null) {
            queueProcessorExecutor.shutdown();
            try {
                if (!queueProcessorExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    logger.warn("队列处理线程池未在5秒内正常关闭，强制终止");
                    queueProcessorExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                logger.error("关闭队列处理线程池时被中断", e);
                queueProcessorExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 记录剩余队列状态
        Map<String, Integer> finalSizes = getQueueSizes();
        logger.info("SymbolBasedMarketDataQueue 已关闭，剩余队列状态: {}", finalSizes);
    }
}

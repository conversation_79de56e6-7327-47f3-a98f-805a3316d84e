package com.flopotech.bg.quote.oz.queue;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import com.flopotech.bg.quote.oz.service.MarketDataProducer;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;

/**
 * 基于配置文件symbol的市场数据队列服务
 * 使用NIO直接内存队列实现零拷贝和高性能处理
 *
 * 设计架构：
 * 1. 接收队列（rawQueue）：快速接收市场数据
 * 2. 排序队列（sortedQueue）：按时间戳排序的数据
 * 3. NIO直接内存：减少内存拷贝，提高性能
 * 4. 双队列设计：数据先进入原始队列，然后转移到排序队列，最后发送到Kafka
 */
@Component
public class SymbolBasedMarketDataQueue {

    private static final Logger logger = LoggerFactory.getLogger(SymbolBasedMarketDataQueue.class);

    @Autowired
    private MarketDataProducer marketDataProducer;

    /**
     * 从配置文件读取订阅的symbol列表
     */
    @Value("${quickfix.onezero.subscribe.symbol}")
    private String subscribeSymbols;

    /**
     * NIO直接内存队列 - 实现零拷贝和高性能处理
     * 包含两个队列：rawQueues（接收队列）和sortedQueues（排序队列）
     */
    private NioDirectMemoryQueue<String> nioQueue;

    /**
     * 配置的symbol列表
     */
    private Set<String> configuredSymbols;

    @PostConstruct
    public void initialize() {
        // 解析配置的symbol列表
        parseConfiguredSymbols();

        // 初始化NIO直接内存队列
        initializeNioQueue();

        logger.info("SymbolBasedMarketDataQueueV2 初始化完成，配置的symbols: {}", configuredSymbols);
    }

    /**
     * 解析配置文件中的symbol列表
     */
    private void parseConfiguredSymbols() {
        if (subscribeSymbols == null || subscribeSymbols.trim().isEmpty()) {
            logger.warn("配置文件中未找到quickfix.onezero.subscribe.symbol配置");
            configuredSymbols = new HashSet<>();
            return;
        }

        configuredSymbols = new HashSet<>();
        String[] symbols = subscribeSymbols.split(",");
        for (String symbol : symbols) {
            String trimmedSymbol = symbol.trim();
            if (!trimmedSymbol.isEmpty()) {
                configuredSymbols.add(trimmedSymbol);
            }
        }

        logger.info("解析到配置的symbols: {}", configuredSymbols);
    }

    /**
     * 初始化NIO直接内存队列
     */
    private void initializeNioQueue() {
        nioQueue = new NioDirectMemoryQueue<>();

        // 注入MarketDataProducer到NIO队列中
        try {
            java.lang.reflect.Field field = NioDirectMemoryQueue.class.getDeclaredField("marketDataProducer");
            field.setAccessible(true);
            field.set(nioQueue, marketDataProducer);

            logger.info("成功注入MarketDataProducer到NIO队列");
        } catch (Exception e) {
            logger.error("注入MarketDataProducer到NIO队列失败: {}", e.getMessage(), e);
            throw new RuntimeException("NIO队列初始化失败", e);
        }

        // 启动NIO队列的处理线程
        nioQueue.startProcessing();

        logger.info("NIO直接内存队列初始化完成，支持零拷贝和双队列架构");
    }

    /**
     * 将市场数据加入队列
     * 
     * 处理流程：
     * 1. 数据首先进入rawQueue（接收队列）
     * 2. 然后转移到sortedQueue（排序队列）按时间戳排序
     * 3. 最后从sortedQueue发送到Kafka
     */
    public boolean enqueue(MarketDataMsg.MarketData marketData) {
        if (marketData == null) {
            logger.warn("尝试入队null市场数据");
            return false;
        }

        String symbol = marketData.getSymbol();
        if (symbol == null || symbol.trim().isEmpty()) {
            logger.warn("市场数据缺少symbol信息");
            return false;
        }

        // 检查symbol是否在配置列表中
        if (!configuredSymbols.contains(symbol)) {
            logger.debug("symbol {} 不在配置的订阅列表中，跳过处理", symbol);
            return false;
        }

        try {
            // 使用NIO队列的enqueue方法
            // 这会将数据先放入rawQueue，然后自动转移到sortedQueue
            nioQueue.enqueue(symbol, marketData);

            logger.debug("市场数据成功入队 - Symbol: {}, Timestamp: {}",
                    symbol, marketData.getTimestamp());

            return true;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("入队被中断 - Symbol: {}, Timestamp: {}",
                    symbol, marketData.getTimestamp());
            return false;
        } catch (IOException e) {
            logger.error("入队IO异常 - Symbol: {}, Timestamp: {}, Error: {}",
                    symbol, marketData.getTimestamp(), e.getMessage(), e);
            return false;
        } catch (Exception e) {
            logger.error("入队异常 - Symbol: {}, Timestamp: {}, Error: {}",
                    symbol, marketData.getTimestamp(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取队列状态信息
     */
    public Map<String, Integer> getQueueSizes() {
        Map<String, Integer> sizes = new HashMap<>();

        // 检查每个配置的symbol的队列状态
        for (String symbol : configuredSymbols) {
            boolean isEmpty = nioQueue.isEmpty(symbol);
            sizes.put(symbol, isEmpty ? 0 : -1); // -1表示非空但无法获取确切大小
        }

        return sizes;
    }

    /**
     * 获取配置的symbol列表
     */
    public Set<String> getConfiguredSymbols() {
        return new HashSet<>(configuredSymbols);
    }

    /**
     * 检查特定symbol的队列是否为空
     */
    public boolean isQueueEmpty(String symbol) {
        if (!configuredSymbols.contains(symbol)) {
            return true;
        }
        return nioQueue.isEmpty(symbol);
    }

    /**
     * 手动从队列中取出数据（用于测试或特殊情况）
     */
    public MarketDataMsg.MarketData dequeue(String symbol) {
        if (!configuredSymbols.contains(symbol)) {
            logger.warn("尝试从未配置的symbol {} 出队", symbol);
            return null;
        }

        try {
            return nioQueue.dequeue(symbol);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("出队被中断 - Symbol: {}", symbol);
            return null;
        }
    }

    /**
     * 获取队列详细信息（用于监控和调试）
     */
    public String getQueueInfo() {
        StringBuilder info = new StringBuilder();
        info.append("=== NIO直接内存队列状态 ===\n");
        info.append("配置的symbols: ").append(configuredSymbols).append("\n");
        info.append("队列类型: 双队列架构（rawQueue + sortedQueue）\n");
        info.append("内存类型: NIO直接内存（零拷贝）\n");

        Map<String, Integer> sizes = getQueueSizes();
        info.append("队列状态:\n");
        for (Map.Entry<String, Integer> entry : sizes.entrySet()) {
            info.append("  ").append(entry.getKey()).append(": ");
            if (entry.getValue() == 0) {
                info.append("空队列");
            } else if (entry.getValue() == -1) {
                info.append("非空队列");
            } else {
                info.append(entry.getValue()).append(" 条消息");
            }
            info.append("\n");
        }

        return info.toString();
    }

    @PreDestroy
    public void shutdown() {
        logger.info("正在关闭SymbolBasedMarketDataQueueV2...");

        if (nioQueue != null) {
            nioQueue.shutdown();
        }

        logger.info("SymbolBasedMarketDataQueueV2 已关闭");
    }
}

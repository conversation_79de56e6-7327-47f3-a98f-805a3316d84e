package com.flopotech.bg.quote.oz.util;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * MarketDataBuilder测试类
 */
class MarketDataBuilderTest {

        private MarketDataBuilder marketDataBuilder;

        @BeforeEach
        void setUp() {
                marketDataBuilder = new MarketDataBuilder();
        }

        @Test
        @DisplayName("测试创建基本市场数据 - BigDecimal版本")
        void testCreateMarketDataWithBigDecimal() {
                BigDecimal bid = new BigDecimal("1.0850");
                BigDecimal offer = new BigDecimal("1.0852");
                long timestamp = System.currentTimeMillis();
                String symbol = "EURUSD";

                MarketDataMsg.MarketData marketData = marketDataBuilder.createMarketData(
                                timestamp, symbol, bid, offer);

                assertNotNull(marketData);
                assertEquals(timestamp, marketData.getTimestamp());
                assertEquals(symbol, marketData.getSymbol());
                assertEquals("1.0850", marketData.getBid());
                assertEquals("1.0852", marketData.getOffer());
                assertEquals("1.0851", marketData.getMid());
        }

        @Test
        @DisplayName("测试创建基本市场数据 - double版本")
        void testCreateMarketDataWithDouble() {
                double bid = 1.0850;
                double offer = 1.0852;
                long timestamp = System.currentTimeMillis();
                String symbol = "EURUSD";

                MarketDataMsg.MarketData marketData = marketDataBuilder.createMarketData(
                                timestamp, symbol, bid, offer);

                assertNotNull(marketData);
                assertEquals(timestamp, marketData.getTimestamp());
                assertEquals(symbol, marketData.getSymbol());
                assertEquals("1.085", marketData.getBid());
                assertEquals("1.0852", marketData.getOffer());
                // 中间价应该是 (1.085 + 1.0852) / 2 = 1.0851
                assertTrue(marketData.getMid().startsWith("1.0851"));
        }

        @Test
        @DisplayName("测试创建深度数据 - BigDecimal版本")
        void testCreateDepthDataWithBigDecimal() {
                BigDecimal price = new BigDecimal("1.0850");
                BigDecimal size = new BigDecimal("1000000");
                int type = 0; // bid
                String condition = "A";
                String origin = "LP1";
                String uniqueId = "bid-001";

                MarketDataMsg.DepthData depthData = marketDataBuilder.createDepthData(
                                type, price, size, condition, origin, uniqueId);

                assertNotNull(depthData);
                assertEquals(type, depthData.getTp());
                assertEquals("1.0850", depthData.getPx());
                assertEquals("1000000", depthData.getSz());
                assertEquals(condition, depthData.getCondition());
                assertEquals(origin, depthData.getOri());
                assertEquals(uniqueId, depthData.getUqId());
        }

        @Test
        @DisplayName("测试创建深度数据 - double版本")
        void testCreateDepthDataWithDouble() {
                double price = 1.0850;
                double size = 1000000.0;
                int type = 1; // offer
                String condition = "I";
                String origin = "LP2";
                String uniqueId = "offer-001";

                MarketDataMsg.DepthData depthData = marketDataBuilder.createDepthData(
                                type, price, size, condition, origin, uniqueId);

                assertNotNull(depthData);
                assertEquals(type, depthData.getTp());
                assertEquals("1.085", depthData.getPx());
                assertTrue(depthData.getSz().startsWith("1000000"));
                assertEquals(condition, depthData.getCondition());
                assertEquals(origin, depthData.getOri());
                assertEquals(uniqueId, depthData.getUqId());
        }

        @Test
        @DisplayName("测试创建包含深度数据的市场数据")
        void testCreateMarketDataWithDepth() {
                BigDecimal bid = new BigDecimal("1.0850");
                BigDecimal offer = new BigDecimal("1.0852");
                long timestamp = System.currentTimeMillis();
                String symbol = "EURUSD";

                // 创建深度数据
                List<MarketDataMsg.DepthData> depthDataList = Arrays.asList(
                                marketDataBuilder.createDepthData(0, bid, new BigDecimal("1000000"), "A", "LP1",
                                                "bid-001"),
                                marketDataBuilder.createDepthData(1, offer, new BigDecimal("2000000"), "A", "LP2",
                                                "offer-001"));

                MarketDataMsg.MarketData marketData = marketDataBuilder.createMarketDataWithDepth(
                                timestamp, symbol, bid, offer, depthDataList);

                assertNotNull(marketData);
                assertEquals(timestamp, marketData.getTimestamp());
                assertEquals(symbol, marketData.getSymbol());
                assertEquals("1.0850", marketData.getBid());
                assertEquals("1.0852", marketData.getOffer());
                assertEquals("1.0851", marketData.getMid());
                assertEquals(2, marketData.getDataCount());
                assertEquals(depthDataList.size(), marketData.getDataList().size());
        }

        @Test
        @DisplayName("测试可交易报价深度数据创建")
        void testCreateTradeableDepthData() {
                BigDecimal price = new BigDecimal("1.0850");
                BigDecimal size = new BigDecimal("1000000");

                MarketDataMsg.DepthData depthData = marketDataBuilder.createTradeableDepthData(
                                0, price, size, "LP1", "bid-001");

                assertNotNull(depthData);
                assertEquals("A", depthData.getCondition()); // 可交易报价
                assertEquals("1.0850", depthData.getPx());
                assertEquals("1000000", depthData.getSz());
        }

        @Test
        @DisplayName("测试参考报价深度数据创建")
        void testCreateIndicativeDepthData() {
                BigDecimal price = new BigDecimal("1.0850");
                BigDecimal size = new BigDecimal("1000000");

                MarketDataMsg.DepthData depthData = marketDataBuilder.createIndicativeDepthData(
                                0, price, size, "LP1", "bid-001");

                assertNotNull(depthData);
                assertEquals("I", depthData.getCondition()); // 参考报价
                assertEquals("1.0850", depthData.getPx());
                assertEquals("1000000", depthData.getSz());
        }

        @Test
        @DisplayName("测试更新市场数据")
        void testUpdateMarketData() {
                // 创建原始市场数据
                BigDecimal originalBid = new BigDecimal("1.0850");
                BigDecimal originalOffer = new BigDecimal("1.0852");
                MarketDataMsg.MarketData original = marketDataBuilder.createMarketData(
                                System.currentTimeMillis(), "EURUSD", originalBid, originalOffer);

                // 更新价格
                BigDecimal newBid = new BigDecimal("1.0851");
                BigDecimal newOffer = new BigDecimal("1.0853");
                MarketDataMsg.MarketData updated = marketDataBuilder.updateMarketData(
                                original, newBid, newOffer);

                assertNotNull(updated);
                assertEquals("EURUSD", updated.getSymbol());
                assertEquals("1.0851", updated.getBid());
                assertEquals("1.0853", updated.getOffer());
                assertEquals("1.0852", updated.getMid());
                assertTrue(updated.getTimestamp() >= original.getTimestamp());
        }

        @Test
        @DisplayName("测试市场数据有效性验证")
        void testIsValidMarketData() {
                // 测试有效的市场数据
                BigDecimal bid = new BigDecimal("1.0850");
                BigDecimal offer = new BigDecimal("1.0852");
                MarketDataMsg.MarketData validData = marketDataBuilder.createMarketData(
                                System.currentTimeMillis(), "EURUSD", bid, offer);
                assertTrue(marketDataBuilder.isValidMarketData(validData));

                // 测试null数据
                assertFalse(marketDataBuilder.isValidMarketData(null));

                // 测试无效的symbol
                MarketDataMsg.MarketData invalidSymbol = MarketDataMsg.MarketData.newBuilder()
                                .setTimestamp(System.currentTimeMillis())
                                .setSymbol("")
                                .setBid("1.0850")
                                .setOffer("1.0852")
                                .setMid("1.0851")
                                .build();
                assertFalse(marketDataBuilder.isValidMarketData(invalidSymbol));

                // 测试无效的时间戳
                MarketDataMsg.MarketData invalidTimestamp = MarketDataMsg.MarketData.newBuilder()
                                .setTimestamp(0)
                                .setSymbol("EURUSD")
                                .setBid("1.0850")
                                .setOffer("1.0852")
                                .setMid("1.0851")
                                .build();
                assertFalse(marketDataBuilder.isValidMarketData(invalidTimestamp));

                // 测试bid >= offer的情况
                MarketDataMsg.MarketData invalidPrices = MarketDataMsg.MarketData.newBuilder()
                                .setTimestamp(System.currentTimeMillis())
                                .setSymbol("EURUSD")
                                .setBid("1.0852")
                                .setOffer("1.0850")
                                .setMid("1.0851")
                                .build();
                assertFalse(marketDataBuilder.isValidMarketData(invalidPrices));
        }

        @Test
        @DisplayName("测试精度保持")
        void testPrecisionMaintenance() {
                // 使用高精度的BigDecimal
                BigDecimal highPrecisionBid = new BigDecimal("1.085012345678901234567890");
                BigDecimal highPrecisionOffer = new BigDecimal("1.085212345678901234567890");

                MarketDataMsg.MarketData marketData = marketDataBuilder.createMarketData(
                                System.currentTimeMillis(), "EURUSD", highPrecisionBid, highPrecisionOffer);

                // 验证精度得到保持
                assertNotNull(marketData.getBid());
                assertNotNull(marketData.getOffer());
                assertTrue(marketData.getBid().contains("1.085012345"));
                assertTrue(marketData.getOffer().contains("1.085212345"));

                // 验证可以正确反序列化
                BigDecimal deserializedBid = BigDecimalUtils.fromString(marketData.getBid());
                BigDecimal deserializedOffer = BigDecimalUtils.fromString(marketData.getOffer());

                assertNotNull(deserializedBid);
                assertNotNull(deserializedOffer);
                assertTrue(deserializedBid.compareTo(BigDecimal.ZERO) > 0);
                assertTrue(deserializedOffer.compareTo(BigDecimal.ZERO) > 0);
        }
}

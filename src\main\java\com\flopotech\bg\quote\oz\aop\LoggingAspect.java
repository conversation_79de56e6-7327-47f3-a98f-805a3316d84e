package com.flopotech.bg.quote.oz.aop;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import quickfix.Message;
import quickfix.SessionID;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * AOP日志切面 - 记录完整类路径、方法名、行号及QuickFIX/J消息
 */
@Aspect
@Component
public class LoggingAspect {

    private static final Logger logger = LoggerFactory.getLogger(LoggingAspect.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();


    /**
     * 环绕通知，记录方法调用日志（包括QuickFIX/J Application方法）
     */
    @Around("execution(* com.flopotech.bg.quote.oz.*.*(..)) || execution(* quickfix.Application+.*(..))")
    public Object logMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        String requestId = UUID.randomUUID().toString();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String className = signature.getDeclaringTypeName();
        String methodName = signature.getName();

        // 获取调用栈以确定行号
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        int lineNumber = getLineNumber(joinPoint);

        String userId = getUserId(); // 模拟获取用户ID

        // 设置MDC上下文
        MDC.put("requestId", requestId);
        MDC.put("userId", userId != null ? userId : "anonymous");
        MDC.put("className", className);
//        MDC.put("methodName", methodName);
//        MDC.put("lineNumber", String.valueOf(lineNumber));

        try {
            // 记录入参（包括QuickFIX/J消息）
            Map<String, Object> params = getMethodParams(joinPoint);
            String paramsJson = formatParams(params);
            MDC.put("params",paramsJson);
            logger.info("Method Start");

            // 执行目标方法
            long startTime = System.currentTimeMillis();
            Object result = joinPoint.proceed();
            long duration = System.currentTimeMillis() - startTime;

            // 记录出参（包括QuickFIX/J消息）
            String resultJson = formatResult(result);
            MDC.put("result",resultJson);
            logger.info("Method Complete | Duration: {}ms",  duration);

            return result;
        } catch (Throwable t) {
            // 记录错误日志
            logger.error("Method Error | Message: {}", t.getMessage(), t);
            throw t;
        } finally {
            // 清理MDC上下文
            MDC.clear();
        }
    }

    /**
     * 获取方法参数（处理QuickFIX/J消息）
     */
    private Map<String, Object> getMethodParams(ProceedingJoinPoint joinPoint) {
        Map<String, Object> params = new HashMap<>();
        String[] paramNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
        Object[] args = joinPoint.getArgs();

        for (int i = 0; i < paramNames.length; i++) {
            if (args[i] instanceof Message) {
                // 处理QuickFIX/J消息
                Message fixMessage = (Message) args[i];
                params.put(paramNames[i], formatFixMessage(fixMessage));
            } else if (args[i] instanceof SessionID) {
                // 处理SessionID
                SessionID sessionID = (SessionID) args[i];
                params.put(paramNames[i], Map.of(
                        "BeginString", sessionID.getBeginString(),
                        "SenderCompID", sessionID.getSenderCompID(),
                        "TargetCompID", sessionID.getTargetCompID()
                ));
            } else {
                params.put(paramNames[i], args[i]);
            }
        }
        return params;
    }

    /**
     * 格式化QuickFIX/J消息
     */
    private Map<String, Object> formatFixMessage(Message message) {
        Map<String, Object> result = new HashMap<>();
        try {
            result.put("MsgType", message.getHeader().getString(35));
            result.put("MsgSeqNum", message.getHeader().getInt(34));
            result.put("SenderCompID", message.getHeader().getString(49));
            result.put("TargetCompID", message.getHeader().getString(56));
            result.put("Message", message.toString().replace("\u0001","|"));
        } catch (Exception e) {
            logger.warn("Failed to parse FIX message", e);
            result.put("Message", message.toString());
        }
        return result;
    }

    /**
     * 格式化入参为JSON字符串
     */
    private String formatParams(Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return "{}";
        }
        try {
            // 脱敏处理
            params.forEach((key, value) -> {
                if (isSensitiveField(key)) {
                    params.put(key, "[MASKED]");
                }
            });
            return objectMapper.writeValueAsString(params);
        } catch (JsonProcessingException e) {
            logger.warn("Failed to serialize params to JSON", e);
            return params.toString();
        }
    }

    /**
     * 格式化出参为JSON字符串
     */
    private String formatResult(Object result) {
        if (result == null) {
            return "null";
        }
        if (result instanceof Message) {
            return formatParams(formatFixMessage((Message) result));
        }
        try {
            return objectMapper.writeValueAsString(result);
        } catch (JsonProcessingException e) {
            logger.warn("Failed to serialize result to JSON", e);
            return result.toString();
        }
    }

    /**
     * 判断是否为敏感字段
     */
    private boolean isSensitiveField(String key) {
        return key.toLowerCase().contains("password") ||
                key.toLowerCase().contains("token") ||
                key.toLowerCase().contains("secret");
    }

    private int getLineNumber(ProceedingJoinPoint joinPoint) {
        return Arrays.stream(Thread.currentThread().getStackTrace())
                .filter(e -> e.getClassName().equals(joinPoint.getSignature().getDeclaringTypeName()))
                .findFirst()
                .map(StackTraceElement::getLineNumber)
                .orElse(-1);
    }

    /**
     * 模拟获取用户ID
     */
    private String getUserId() {
        // 实际实现中从Spring Security或QuickFIX/J Session获取
        return "user123";
    }
}

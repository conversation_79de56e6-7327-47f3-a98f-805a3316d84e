package com.flopotech.bg.quote.oz.service;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import com.flopotech.bg.quote.oz.queue.NioDirectMemoryQueue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 直接使用NioDirectMemoryQueue的测试
 * 验证简化后的架构：MarketDataSaveQueueService → NioDirectMemoryQueue
 */
class DirectNioQueueTest {

    private MarketDataSaveQueueService service;
    private NioDirectMemoryQueue<String> nioQueue;

    @BeforeEach
    void setUp() {
        service = new MarketDataSaveQueueService();
        nioQueue = new NioDirectMemoryQueue<>();

        // 注入依赖
        ReflectionTestUtils.setField(service, "nioQueue", nioQueue);
        ReflectionTestUtils.setField(service, "subscribeSymbols", "GBPUSD,EURUSD");
        // 不注入marketDataProducer，测试时会处理null情况

        // 初始化服务
        service.initialize();
    }

    @Test
    @DisplayName("测试简化架构初始化")
    void testSimplifiedArchitectureInitialization() {
        // 验证配置解析
        var configuredSymbols = (java.util.Set<String>) ReflectionTestUtils.getField(service, "configuredSymbols");
        assertNotNull(configuredSymbols);
        assertEquals(2, configuredSymbols.size());
        assertTrue(configuredSymbols.contains("GBPUSD"));
        assertTrue(configuredSymbols.contains("EURUSD"));

        System.out.println("✅ 简化架构初始化成功");
        System.out.println("配置的symbols: " + configuredSymbols);
    }

    @Test
    @DisplayName("测试直接NIO队列操作")
    void testDirectNioQueueOperation() throws Exception {
        // 创建测试数据
        MarketDataMsg.MarketData marketData = MarketDataMsg.MarketData.newBuilder()
                .setSymbol("GBPUSD")
                .setTimestamp(System.currentTimeMillis())
                .setBid("1.2500")
                .setOffer("1.2502")
                .build();

        // 直接使用NIO队列
        assertDoesNotThrow(() -> {
            nioQueue.enqueue("GBPUSD", marketData);
        });

        System.out.println("✅ 直接NIO队列操作成功");
    }

    @Test
    @DisplayName("测试配置驱动的symbol过滤")
    void testConfigurationDrivenSymbolFiltering() {
        // 模拟MarketDataSaveQueueService的symbol检查逻辑
        var configuredSymbols = (java.util.Set<String>) ReflectionTestUtils.getField(service, "configuredSymbols");

        // 配置的symbol应该被接受
        assertTrue(configuredSymbols.contains("GBPUSD"));
        assertTrue(configuredSymbols.contains("EURUSD"));

        // 未配置的symbol应该被拒绝
        assertFalse(configuredSymbols.contains("USDJPY"));
        assertFalse(configuredSymbols.contains("UNKNOWN"));

        System.out.println("✅ Symbol过滤逻辑正确");
        System.out.println("接受的symbols: " + configuredSymbols);
    }

    @Test
    @DisplayName("验证架构简化效果")
    void testArchitectureSimplification() {
        System.out.println("=== 架构简化验证 ===");
        System.out.println("旧架构: MarketDataSaveQueueService → SymbolBasedMarketDataQueue → NioDirectMemoryQueue");
        System.out.println("新架构: MarketDataSaveQueueService → NioDirectMemoryQueue");
        System.out.println("✅ 减少了一层抽象，架构更清晰");

        // 验证直接注入的NIO队列
        assertNotNull(ReflectionTestUtils.getField(service, "nioQueue"));
        assertSame(nioQueue, ReflectionTestUtils.getField(service, "nioQueue"));

        System.out.println("✅ NIO队列直接注入成功");
    }

    @Test
    @DisplayName("测试双队列架构仍然存在")
    void testDualQueueArchitectureStillExists() {
        // 验证NioDirectMemoryQueue内部仍然有双队列
        try {
            var rawQueues = ReflectionTestUtils.getField(nioQueue, "rawQueues");
            var sortedQueues = ReflectionTestUtils.getField(nioQueue, "sortedQueues");

            assertNotNull(rawQueues);
            assertNotNull(sortedQueues);

            System.out.println("✅ 双队列架构仍然存在于NioDirectMemoryQueue中");
            System.out.println("rawQueues: " + rawQueues.getClass().getSimpleName());
            System.out.println("sortedQueues: " + sortedQueues.getClass().getSimpleName());

        } catch (Exception e) {
            System.out.println("⚠️ 无法通过反射访问内部队列，但这不影响功能");
        }
    }

    @Test
    @DisplayName("测试NIO直接内存特性")
    void testNioDirectMemoryFeatures() {
        try {
            var directBuffers = ReflectionTestUtils.getField(nioQueue, "directBuffers");
            assertNotNull(directBuffers);

            System.out.println("✅ NIO直接内存缓冲区存在");
            System.out.println("directBuffers: " + directBuffers.getClass().getSimpleName());

        } catch (Exception e) {
            System.out.println("⚠️ 无法通过反射访问直接内存缓冲区，但这不影响功能");
        }
    }
}

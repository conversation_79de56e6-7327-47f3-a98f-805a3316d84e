package com.flopotech.bg.quote.oz.service;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import com.flopotech.bg.quote.oz.queue.SymbolBasedMarketDataQueue;
import com.flopotech.bg.quote.oz.util.BigDecimalUtils;
import com.flopotech.bg.quote.oz.util.MarketDataBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import quickfix.FieldNotFound;
import quickfix.field.*;
import quickfix.fix44.MarketDataSnapshotFullRefresh;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 市场数据服务 保存至 堆外队列
 * 1.判断数据是否过期, 数据增加是否过期异常标记
 * 2.数据增加是当前时间
 */
@Service
public class MarketDataSaveQueueService {

    private static final Logger logger = LoggerFactory.getLogger(MarketDataSaveQueueService.class);

    // @Autowired
    // private NioDirectMemoryQueue nioDirectMemoryQueue;

    @Autowired
    private MarketDataBuilder marketDataBuilder;

    @Autowired
    private MarketDataProducer marketDataProducer;

    @Autowired
    private SymbolBasedMarketDataQueue symbolBasedQueue;

    /**
     * 缓存容器：存储 symbol 对应的最新时间戳（毫秒级）
     * 线程安全，适合多线程并发读写
     */
    private final ConcurrentMap<String, Long> symbolTimeMap = new ConcurrentHashMap<>();

    /**
     * 处理新数据，判断是否过期并更新缓存
     * 
     * @param symbol      数据类型标识（如 "stock_A"、"sensor_001"）
     * @param newDataTime 新数据的时间戳（毫秒级，需与第三方数据对齐）
     * @return true=数据过期；false=数据有效（已更新缓存）
     */
    private boolean processNewData(String symbol, long newDataTime) {
        // 原子操作：通过 compute 方法保证整个过程的线程安全
        Long result = symbolTimeMap.compute(symbol, (key, oldTime) -> {
            if (oldTime == null) {
                // 首次出现该 symbol：直接存入新时间戳（不视为过期）
                return newDataTime;
            } else if (newDataTime > oldTime) {
                // 新时间戳更大：更新缓存（数据有效）
                return newDataTime;
            } else {
                // 新时间戳更小：保留旧时间戳（数据过期）
                return oldTime;
            }
        });

        // 判断是否过期：若旧时间存在且新时间更小，则过期
        return oldTimeExistsAndNewer(result, newDataTime);
    }

    /**
     * 辅助方法：判断是否因新时间更小而过期
     */
    private boolean oldTimeExistsAndNewer(Long oldTime, long newDataTime) {
        // compute 方法返回的是更新后的值（即 oldTime 或 newDataTime）
        // 若更新后的值等于旧时间（说明未更新），且新时间 < 旧时间 → 过期
        return oldTime.equals(oldTime) && newDataTime < oldTime;
    }

    /**
     * 获取某个 symbol 的最新时间戳（用于调试或监控）
     */
    private Long getLatestTime(String symbol) {
        return symbolTimeMap.get(symbol);
    }

    /**
     * 手动清理过期数据（建议定期调用，如每小时一次）
     * 清理逻辑：删除超过指定时间未更新的条目（例如 24 小时）
     * 
     * @param expiredThreshold 过期阈值（毫秒，如 24 * 3600 * 1000 = 86400000）
     */
    private void cleanExpiredData(long expiredThreshold) {
        long currentTime = System.currentTimeMillis();
        Iterator<Map.Entry<String, Long>> iterator = symbolTimeMap.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<String, Long> entry = iterator.next();
            Long lastTime = entry.getValue();
            if (currentTime - lastTime > expiredThreshold) {
                iterator.remove(); // 线程安全的删除操作
            }
        }
    }

    /**
     * 清空所有缓存（可选，根据业务需求）
     */
    private void clear() {
        symbolTimeMap.clear();
    }

    /**
     * 处理市场数据快照
     */
    public void handleMarketDataSnapshot(MarketDataSnapshotFullRefresh message)
            throws FieldNotFound, IOException, InterruptedException {

        Date receiveTime = new Date();// 接收数据时间
        String symbol = message.getString(Symbol.FIELD);
        LocalDateTime d = message.getHeader().getUtcTimeStamp(SendingTime.FIELD);
        Long s = d.atZone(ZoneId.of("UTC")).toInstant().toEpochMilli();
        boolean isOld = processNewData(symbol, s); // 是否为旧数据
        int oldData = isOld ? 1 : 0; // 行情数据是否为旧数据 1-是 0-否

        BigDecimal optimalBid = null; // 最优bid价格
        BigDecimal optimalOffer = null; // 最优offer价格

        List<MarketDataMsg.DepthData> dpList = new ArrayList<>();
        // 处理市场数据条目
        int noMDEntries = message.getInt(NoMDEntries.FIELD);
        for (int i = 1; i <= noMDEntries; i++) {
            MarketDataSnapshotFullRefresh.NoMDEntries group = new MarketDataSnapshotFullRefresh.NoMDEntries();
            message.getGroup(i, group);

            char entryType = group.getChar(MDEntryType.FIELD);
            double priceDouble = group.getDouble(MDEntryPx.FIELD);
            double sizeDouble = group.getDouble(MDEntrySize.FIELD);

            // 转换为BigDecimal以保证精度
            BigDecimal price = BigDecimalUtils.fromDouble(priceDouble);
            BigDecimal size = BigDecimalUtils.fromDouble(sizeDouble);

            String entryId = group.getString(QuoteEntryID.FIELD);
            String entryTypeStr = (entryType == MDEntryType.BID) ? "BID" : "OFFER";
            String condition = group.getString(QuoteCondition.FIELD);
            String ori = group.getString(MDEntryOriginator.FIELD);

            if ("BID".equals(entryTypeStr)) {
                if (optimalBid == null || (optimalBid.compareTo(price) <= 0)) {
                    optimalBid = price;
                }
            } else if ("OFFER".equals(entryTypeStr)) {
                if (optimalOffer == null || (optimalOffer.compareTo(price) >= 0)) {
                    optimalOffer = price;
                }
            }

            MarketDataMsg.DepthData depthData = marketDataBuilder
                    .createDepthData(Integer.parseInt(String.valueOf(entryType)), price, size, condition, ori, entryId);
            dpList.add(depthData);
        }
        MarketDataMsg.MarketData msg = marketDataBuilder.createMarketDataWithDepth(s, receiveTime.getTime(), oldData,
                symbol, optimalBid, optimalOffer, dpList);

        // 使用基于symbol的队列进行分片处理
        boolean enqueued = symbolBasedQueue.enqueue(msg);
        if (!enqueued) {
            logger.warn("市场数据入队失败，直接发送到Kafka - Symbol: {}", symbol);
            // 如果入队失败，直接发送到Kafka作为备用方案
            marketDataProducer.sendMarketData(msg);
        } else {
            logger.debug("市场数据已入队等待处理 - Symbol: {}, Timestamp: {}", symbol, s);
        }
    }

}

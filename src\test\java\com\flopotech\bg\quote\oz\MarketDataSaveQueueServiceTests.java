package com.flopotech.bg.quote.oz;


import com.flopotech.bg.quote.oz.service.MarketDataSaveQueueService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import quickfix.field.*;
import quickfix.fix44.MarketDataSnapshotFullRefresh;

import java.math.BigDecimal;

@SpringBootTest
public class MarketDataSaveQueueServiceTests {



    @Autowired
    private MarketDataSaveQueueService marketDataSaveOffHeapQueueService;


    @Test
    void marketDataSaveOffHeapQueueTest() {

        System.out.println("=======marketDataSaveOffHeapQueueTest  start============");

        String mdReqID = "MD_REQ_1753862467181_5afb8eff";
        String symbol = "EURUSD";
        MarketDataSnapshotFullRefresh message = createSubscriptionRequest(mdReqID , symbol);
        try {
            marketDataSaveOffHeapQueueService.handleMarketDataSnapshot(message);
            Thread.sleep(1000 * 60 * 3);//防止异步原因 , Kafka相关服务提前销毁 , 后面保存kafka出现异常
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }

        System.out.println("=======marketDataSaveOffHeapQueueTest  end============");
    }






    private MarketDataSnapshotFullRefresh createSubscriptionRequest(String mdReqID, String symbol) {

        // 1. 创建行情快照消息对象
        MarketDataSnapshotFullRefresh mdsfr = new MarketDataSnapshotFullRefresh();

        // 设置请求ID
        mdsfr.getHeader().setString(MDReqID.FIELD , mdReqID);
        // 2. 设置头部必要字段（发送方/接收方标识）
        mdsfr.getHeader().setString(SenderCompID.FIELD, "EBC_UAT_Q"); // 发送方ID
        mdsfr.getHeader().setString(TargetCompID.FIELD, "MT4-1_UAT_Q");   // 接收方ID（如交易所）
        mdsfr.getHeader().setField(new BeginString("FIX.4.4"));       // 必选：协议版本
        mdsfr.getHeader().setField(new MsgType("WS"));                // 必选：消息类型（WS=行情快照）
        mdsfr.getHeader().setField(new MsgSeqNum(123));               // 必选：消息序列号
        mdsfr.getHeader().setField(new SendingTime());                // 必选：发送时间（自动填充当前时间）

        // 3. 设置消息体基础字段
        mdsfr.setString(Symbol.FIELD, symbol); // 标的代码
        mdsfr.setInt(267, 0); // 267是MarketDataUpdateType的标准标签号，0=全量刷新

        // 4. 设置行情条目数量（NoMDEntries）
        int entryCount = 3; // 包含：最新价、买一、卖一
        mdsfr.setInt(NoMDEntries.FIELD, entryCount);

        // 5. 添加行情条目（重复组 NoMDEntries）
        // 条目1：最新成交价（MDEntryType=2）
        MarketDataSnapshotFullRefresh.NoMDEntries entry1 = new MarketDataSnapshotFullRefresh.NoMDEntries();
        entry1.setChar(MDEntryType.FIELD, '0'); // 0=买价
        entry1.setDecimal(MDEntryPx.FIELD, new BigDecimal("1.33644")); // 价格
        entry1.setDecimal(MDEntrySize.FIELD, new BigDecimal("500000")); // 成交量
        entry1.setString(QuoteEntryID.FIELD ,"243559103");
        entry1.setString(QuoteCondition.FIELD ,"A");
        entry1.setString(MDEntryOriginator.FIELD ,"VEL000769");
        mdsfr.addGroup(entry1);


        // 条目2：买一价（MDEntryType=0）
        MarketDataSnapshotFullRefresh.NoMDEntries entry2 = new MarketDataSnapshotFullRefresh.NoMDEntries();
        entry2.setChar(MDEntryType.FIELD, '0'); // 0=买价
        entry2.setDecimal(MDEntryPx.FIELD, new BigDecimal("1.33643")); // 买一价
        entry2.setDecimal(MDEntrySize.FIELD, new BigDecimal("1500000")); // 买一量
        entry2.setString(QuoteEntryID.FIELD ,"243559106");
        entry2.setString(QuoteCondition.FIELD ,"A");
        entry2.setString(MDEntryOriginator.FIELD ,"VEL000769");
        mdsfr.addGroup(entry2);

        // 条目3：卖一价（MDEntryType=1）
        MarketDataSnapshotFullRefresh.NoMDEntries entry3 = new MarketDataSnapshotFullRefresh.NoMDEntries();
        entry3.setChar(MDEntryType.FIELD, '1'); // 1=卖价
        entry3.setDecimal(MDEntryPx.FIELD, new BigDecimal("1.33645")); // 卖一价
        entry3.setDecimal(MDEntrySize.FIELD, new BigDecimal("2500000")); // 卖一量
        entry3.setString(QuoteEntryID.FIELD ,"243559108");
        entry3.setString(QuoteCondition.FIELD ,"A");
        entry3.setString(MDEntryOriginator.FIELD ,"VEL000769");
        mdsfr.addGroup(entry3);


        // 条目3：卖一价（MDEntryType=1）
        MarketDataSnapshotFullRefresh.NoMDEntries entry4 = new MarketDataSnapshotFullRefresh.NoMDEntries();
        entry4.setChar(MDEntryType.FIELD, '1'); // 1=卖价
        entry4.setDecimal(MDEntryPx.FIELD, new BigDecimal("1.33646")); // 卖一价
        entry4.setDecimal(MDEntrySize.FIELD, new BigDecimal("1600000")); // 卖一量
        entry4.setString(QuoteEntryID.FIELD ,"243559109");
        entry4.setString(QuoteCondition.FIELD ,"A");
        entry4.setString(MDEntryOriginator.FIELD ,"VEL000769");
        mdsfr.addGroup(entry4);

        return mdsfr;
    }





}

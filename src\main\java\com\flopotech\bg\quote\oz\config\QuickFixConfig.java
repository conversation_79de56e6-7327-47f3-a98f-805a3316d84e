package com.flopotech.bg.quote.oz.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import quickfix.*;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * QuickFIX/J 配置类
 */
@Configuration
public class QuickFixConfig {

    private static final Logger logger = LoggerFactory.getLogger(QuickFixConfig.class);

    @Value("${quickfix.client.config}")
    private String clientConfigFile;

    @Value("${quickfix.client.auto-startup:true}")
    private boolean autoStartup;

    private SocketInitiator initiator;

    /**
     * 初始化TLS配置，跳过证书验证
     */
    @PostConstruct
    public void initTlsConfig() {
        // 强制使用TLS v1.2
        System.setProperty("https.protocols", "TLSv1.2");
        System.setProperty("jdk.tls.client.protocols", "TLSv1.2");

        // 禁用证书验证（处理OneZero过期证书）
        System.setProperty("com.sun.net.ssl.checkRevocation", "false");
        System.setProperty("sun.security.ssl.allowUnsafeRenegotiation", "true");
        System.setProperty("sun.security.ssl.allowLegacyHelloMessages", "true");

        // 创建信任所有证书的SSL上下文
        try {
            javax.net.ssl.TrustManager[] trustAllCerts = new javax.net.ssl.TrustManager[] {
                    new javax.net.ssl.X509TrustManager() {
                        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                            return null;
                        }

                        public void checkClientTrusted(java.security.cert.X509Certificate[] certs, String authType) {
                            // 跳过客户端证书验证
                        }

                        public void checkServerTrusted(java.security.cert.X509Certificate[] certs, String authType) {
                            // 跳过服务器证书验证（包括过期证书）
                            logger.debug("跳过服务器证书验证: authType={}, 证书数量={}", authType, certs != null ? certs.length : 0);
                        }
                    }
            };

            // 创建SSL上下文 - 支持TLSv1（Trade环境要求）
            javax.net.ssl.SSLContext sslContext = javax.net.ssl.SSLContext.getInstance("TLSv1");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

            // 设置为默认SSL上下文
            javax.net.ssl.SSLContext.setDefault(sslContext);

            // 禁用主机名验证
            javax.net.ssl.HttpsURLConnection.setDefaultHostnameVerifier(
                    new javax.net.ssl.HostnameVerifier() {
                        public boolean verify(String hostname, javax.net.ssl.SSLSession session) {
                            return true; // 跳过主机名验证
                        }
                    });

            logger.info("✅ TLS v1.2 配置已初始化，跳过证书验证（处理OneZero过期证书）");

        } catch (Exception e) {
            logger.error("初始化SSL配置失败", e);
        }

        // 启用TLS调试（用于排查SSL握手问题）
        System.setProperty("javax.net.debug", "ssl,handshake");

        // 添加SSL线程安全配置
        System.setProperty("javax.net.ssl.sessionCacheSize", "0");
        System.setProperty("javax.net.ssl.sessionCacheTimeout", "0");
    }

    /**
     * 创建客户端会话设置
     */
    @Bean(name = "clientSessionSettings")
    public SessionSettings clientSessionSettings() throws ConfigError {
//        SessionSettings settings=new SessionSettings();
//        settings.setString("ConnectionType", "initiator");
//        settings.setString("ReconnectInterval", "30");
//        settings.setString("FileStorePath", "quickfix/onezero/store");
//        settings.setString("FileLogPath", "quickfix/onezero/log");
//        settings.setString("ScreenLogShowIncoming", "Y");
//        settings.setString("ScreenLogShowOutgoing", "Y");
//        settings.setString("ScreenLogShowEvents", "Y");
//        settings.setString("StartTime", "00:00:00");
//        settings.setString("EndTime", "00:00:00");
//        settings.setString("UseDataDictionary", "Y");
//        settings.setString("DataDictionary", "FIX44.xml");
//        settings.setString("HeartBtInt", "15");
//        settings.setString("ResetOnLogon", "Y");
//        settings.setString("ResetOnLogout", "Y");
//        settings.setString("ResetOnDisconnect", "Y");
//        SessionID sessionId = new SessionID("FIX.4.4", "MT4-1_UAT_Q", "EBC_UAT_Q");
//        settings.setString(sessionId,"SocketUseSSL","N");
//        settings.setString(sessionId,"CipherSuites","TLS_RSA_WITH_AES_128_CBC_SHA");
//        settings.setString(sessionId,"EnabledProtocols","TLSv1.2");
//        settings.setString(sessionId,"SocketCheckCertificate","N");
//        settings.setString(sessionId,"SocketVerifyHostname","N");
//        settings.setString(sessionId,"SocketTrustAllCertificates","Y");
//        settings.setString(sessionId,"SocketReuseAddress","Y");
//        settings.setString(sessionId,"SocketTcpNoDelay","Y");
//        settings.setString(sessionId,"SocketKeepAlive","Y");
//        settings.setString(sessionId,"SocketConnectHost","**************");
//        settings.setString(sessionId,"SocketConnectPort","10000");
//        settings.setString(sessionId,"Password","44wzBb4N6BDq");
//        settings.setString(sessionId,"SocketConnectTimeout","30000");
//        settings.setString(sessionId,"SocketKeepAlive","Y");
//        return settings;
        try {
            // 首先尝试从外部文件加载（Docker环境）
            if (clientConfigFile.startsWith("/")) {
                return new SessionSettings(clientConfigFile);
            }

            // 然后尝试从classpath加载（本地开发环境）
            InputStream inputStream = getClass().getClassLoader()
                    .getResourceAsStream(clientConfigFile.replace("classpath:", ""));

            if (inputStream == null) {
                throw new ConfigError("无法找到配置文件: " + clientConfigFile);
            }

            return new SessionSettings(inputStream);
        } catch (Exception e) {
            logger.error("加载QuickFIX配置文件失败: {}", clientConfigFile, e);
            throw new ConfigError("加载QuickFIX配置文件失败: " + e.getMessage());
        }
    }

    /**
     * 创建消息存储工厂
     */
    @Bean
    public MessageStoreFactory messageStoreFactory(
            @Qualifier("clientSessionSettings") SessionSettings sessionSettings) {
        return new FileStoreFactory(sessionSettings);
    }

    /**
     * 创建日志工厂
     */
    @Bean
    public LogFactory logFactory(@Qualifier("clientSessionSettings") SessionSettings sessionSettings) {
        return new FileLogFactory(sessionSettings);
    }

    /**
     * 创建消息工厂
     */
    @Bean
    public MessageFactory messageFactory() {
        return new DefaultMessageFactory();
    }

    /**
     * 创建SocketInitiator with SSL support
     */
    @Bean
    public SocketInitiator socketInitiator(
            @Qualifier("clientSessionSettings") SessionSettings sessionSettings,
            MessageStoreFactory messageStoreFactory,
            LogFactory logFactory,
            MessageFactory messageFactory,
            Application application) throws ConfigError {

        // 创建支持SSL的SocketInitiator
        try {
            // 检查SSL配置，但不强制覆盖配置文件设置
            java.util.Iterator<SessionID> sessionIDs = sessionSettings.sectionIterator();
            while (sessionIDs.hasNext()) {
                SessionID sessionID = sessionIDs.next();

                // 只有当配置文件中启用SSL时才设置SSL相关参数
                if (sessionSettings.isSetting(sessionID, "SocketUseSSL") &&
                        sessionSettings.getBool(sessionID, "SocketUseSSL")) {

                    sessionSettings.setString(sessionID, "SocketEnabledProtocols", "TLSv1.2");
                    sessionSettings.setString(sessionID, "SocketCheckCertificate", "N");
                    sessionSettings.setString(sessionID, "SocketVerifyHostname", "N");
                    sessionSettings.setString(sessionID, "SocketTrustAllCertificates", "Y");

                    // 移除可能存在的空keystore配置
                    if (sessionSettings.isSetting(sessionID, "SocketKeyStore")) {
                        sessionSettings.removeSetting(sessionID, "SocketKeyStore");
                    }
                    if (sessionSettings.isSetting(sessionID, "SocketKeyStorePassword")) {
                        sessionSettings.removeSetting(sessionID, "SocketKeyStorePassword");
                    }
                    if (sessionSettings.isSetting(sessionID, "SocketTrustStore")) {
                        sessionSettings.removeSetting(sessionID, "SocketTrustStore");
                    }
                    if (sessionSettings.isSetting(sessionID, "SocketTrustStorePassword")) {
                        sessionSettings.removeSetting(sessionID, "SocketTrustStorePassword");
                    }

                    logger.info("为会话 {} 配置SSL参数（无keystore）", sessionID);
                } else {
                    logger.info("会话 {} 使用普通TCP连接（SSL已禁用）", sessionID);
                }
            }

            this.initiator = new SocketInitiator(application, messageStoreFactory, sessionSettings, logFactory,
                    messageFactory);

            // 验证SSL配置
            boolean sslEnabled = false;
            java.util.Iterator<SessionID> checkSessionIDs = sessionSettings.sectionIterator();
            while (checkSessionIDs.hasNext()) {
                SessionID sessionID = checkSessionIDs.next();
                if (sessionSettings.isSetting(sessionID, "SocketUseSSL") &&
                        sessionSettings.getBool(sessionID, "SocketUseSSL")) {
                    sslEnabled = true;
                    break;
                }
            }

            logger.info("✅ SocketInitiator已创建，SSL支持: {}", sslEnabled ? "启用" : "禁用");

        } catch (Exception e) {
            logger.error("创建SocketInitiator失败", e);
            throw new ConfigError("创建SocketInitiator失败: " + e.getMessage());
        }

        return this.initiator;
    }

    /**
     * Spring容器启动完成后自动启动QuickFIX会话
     */
    @EventListener(ContextRefreshedEvent.class)
    public void startQuickFixSession() {
        if (autoStartup && initiator != null) {
            try {
                logger.info("正在启动QuickFIX会话...");
                initiator.start();
                logger.info("QuickFIX会话启动成功");
            } catch (Exception e) {
                logger.error("QuickFIX会话启动失败", e);
            }
        } else {
            logger.info("QuickFIX自动启动已禁用");
        }
    }

    /**
     * 应用关闭时停止QuickFIX会话
     */
    @PreDestroy
    public void stopQuickFixSession() {
        if (initiator != null) {
            try {
                logger.info("正在停止QuickFIX会话...");
                initiator.stop();
                logger.info("QuickFIX会话已停止");
            } catch (Exception e) {
                logger.error("停止QuickFIX会话时出错", e);
            }
        }
    }

}

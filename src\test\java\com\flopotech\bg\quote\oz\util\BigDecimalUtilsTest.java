package com.flopotech.bg.quote.oz.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import com.flopotech.bg.quote.oz.msg.MarketDataMsg;

/**
 * BigDecimalUtils工具类测试
 */
class BigDecimalUtilsTest {

    @Test
    @DisplayName("测试BigDecimal转字符串")
    void testToString() {
        // 测试正常值
        BigDecimal value = new BigDecimal("1.23456789");
        assertEquals("1.23456789", BigDecimalUtils.toString(value));

        // 测试null值
        assertEquals("0", BigDecimalUtils.toString(null));

        // 测试零值
        assertEquals("0", BigDecimalUtils.toString(BigDecimal.ZERO));

        // 测试大数值
        BigDecimal largeValue = new BigDecimal("123456789.987654321");
        assertEquals("123456789.987654321", BigDecimalUtils.toString(largeValue));
    }

    @Test
    @DisplayName("测试字符串转BigDecimal")
    void testFromString() {
        // 测试正常值
        BigDecimal result = BigDecimalUtils.fromString("1.23456789");
        assertEquals(new BigDecimal("1.23456789"), result);

        // 测试null值
        assertEquals(BigDecimal.ZERO, BigDecimalUtils.fromString(null));

        // 测试空字符串
        assertEquals(BigDecimal.ZERO, BigDecimalUtils.fromString(""));

        // 测试空白字符串
        assertEquals(BigDecimal.ZERO, BigDecimalUtils.fromString("   "));

        // 测试无效字符串
        assertEquals(BigDecimal.ZERO, BigDecimalUtils.fromString("invalid"));

        // 测试科学计数法
        BigDecimal scientific = BigDecimalUtils.fromString("1.23E+2");
        assertEquals(new BigDecimal("123"), scientific);
    }

    @Test
    @DisplayName("测试double转BigDecimal")
    void testFromDouble() {
        // 测试正常值
        BigDecimal result = BigDecimalUtils.fromDouble(1.23456);
        assertNotNull(result);
        assertTrue(result.compareTo(BigDecimal.ZERO) > 0);

        // 测试零值
        assertEquals(0, BigDecimalUtils.fromDouble(0.0).compareTo(BigDecimal.ZERO));

        // 测试负值
        BigDecimal negative = BigDecimalUtils.fromDouble(-1.23);
        assertTrue(negative.compareTo(BigDecimal.ZERO) < 0);

        // 测试NaN
        assertEquals(BigDecimal.ZERO, BigDecimalUtils.fromDouble(Double.NaN));

        // 测试无穷大
        assertEquals(BigDecimal.ZERO, BigDecimalUtils.fromDouble(Double.POSITIVE_INFINITY));
        assertEquals(BigDecimal.ZERO, BigDecimalUtils.fromDouble(Double.NEGATIVE_INFINITY));
    }

    @Test
    @DisplayName("测试BigDecimal转double")
    void testToDouble() {
        // 测试正常值
        BigDecimal value = new BigDecimal("1.23456");
        double result = BigDecimalUtils.toDouble(value);
        assertEquals(1.23456, result, 0.00001);

        // 测试null值
        assertEquals(0.0, BigDecimalUtils.toDouble(null));

        // 测试零值
        assertEquals(0.0, BigDecimalUtils.toDouble(BigDecimal.ZERO));
    }

    @Test
    @DisplayName("测试安全加法运算")
    void testSafeAdd() {
        BigDecimal a = new BigDecimal("1.23");
        BigDecimal b = new BigDecimal("2.34");
        BigDecimal result = BigDecimalUtils.safeAdd(a, b);
        assertEquals(new BigDecimal("3.57"), result);

        // 测试null值
        assertEquals(new BigDecimal("1.23"), BigDecimalUtils.safeAdd(a, null));
        assertEquals(new BigDecimal("2.34"), BigDecimalUtils.safeAdd(null, b));
        assertEquals(BigDecimal.ZERO, BigDecimalUtils.safeAdd(null, null));
    }

    @Test
    @DisplayName("测试安全减法运算")
    void testSafeSubtract() {
        BigDecimal a = new BigDecimal("3.57");
        BigDecimal b = new BigDecimal("1.23");
        BigDecimal result = BigDecimalUtils.safeSubtract(a, b);
        assertEquals(new BigDecimal("2.34"), result);

        // 测试null值
        assertEquals(new BigDecimal("3.57"), BigDecimalUtils.safeSubtract(a, null));
        assertEquals(new BigDecimal("-1.23"), BigDecimalUtils.safeSubtract(null, b));
    }

    @Test
    @DisplayName("测试安全乘法运算")
    void testSafeMultiply() {
        BigDecimal a = new BigDecimal("2.5");
        BigDecimal b = new BigDecimal("4.0");
        BigDecimal result = BigDecimalUtils.safeMultiply(a, b);
        assertEquals(0, result.compareTo(new BigDecimal("10.0")));

        // 测试null值
        assertEquals(0, BigDecimalUtils.safeMultiply(a, null).compareTo(BigDecimal.ZERO));
        assertEquals(0, BigDecimalUtils.safeMultiply(null, b).compareTo(BigDecimal.ZERO));
    }

    @Test
    @DisplayName("测试安全除法运算")
    void testSafeDivide() {
        BigDecimal a = new BigDecimal("10.0");
        BigDecimal b = new BigDecimal("2.5");
        BigDecimal result = BigDecimalUtils.safeDivide(a, b);
        assertEquals(0, result.compareTo(new BigDecimal("4.0")));

        // 测试除以零
        assertEquals(BigDecimal.ZERO, BigDecimalUtils.safeDivide(a, BigDecimal.ZERO));
        assertEquals(BigDecimal.ZERO, BigDecimalUtils.safeDivide(a, null));
    }

    @Test
    @DisplayName("测试中间价计算")
    void testCalculateMid() {
        BigDecimal bid = new BigDecimal("1.0850");
        BigDecimal offer = new BigDecimal("1.0852");
        BigDecimal mid = BigDecimalUtils.calculateMid(bid, offer);
        assertEquals(new BigDecimal("1.0851"), mid);

        // 测试null值
        BigDecimal result = BigDecimalUtils.calculateMid(null, offer);
        assertEquals(new BigDecimal("0.5426"), result);
    }

    @Test
    @DisplayName("测试价格有效性验证")
    void testIsValidPrice() {
        // 测试有效价格
        assertTrue(BigDecimalUtils.isValidPrice(new BigDecimal("1.23")));
        assertTrue(BigDecimalUtils.isValidPrice(new BigDecimal("0.01")));

        // 测试无效价格
        assertFalse(BigDecimalUtils.isValidPrice(BigDecimal.ZERO));
        assertFalse(BigDecimalUtils.isValidPrice(new BigDecimal("-1.23")));
        assertFalse(BigDecimalUtils.isValidPrice(null));
    }

    @Test
    @DisplayName("测试数量有效性验证")
    void testIsValidSize() {
        // 测试有效数量
        assertTrue(BigDecimalUtils.isValidSize(new BigDecimal("1000000")));
        assertTrue(BigDecimalUtils.isValidSize(BigDecimal.ZERO));

        // 测试无效数量
        assertFalse(BigDecimalUtils.isValidSize(new BigDecimal("-1000")));
        assertFalse(BigDecimalUtils.isValidSize(null));
    }

    @Test
    @DisplayName("测试格式化输出")
    void testFormatToString() {
        BigDecimal value = new BigDecimal("1.23456789");

        // 测试指定小数位数
        assertEquals("1.23456789", BigDecimalUtils.formatToString(value, 8));
        assertEquals("1.23", BigDecimalUtils.formatToString(value, 2));
        assertEquals("1", BigDecimalUtils.formatToString(value, 0));

        // 测试默认小数位数
        assertEquals("1.23456789", BigDecimalUtils.formatToString(value));

        // 测试null值
        assertEquals("0", BigDecimalUtils.formatToString(null));
    }

    @Test
    @DisplayName("测试精度保持")
    void testPrecisionMaintenance() {
        // 测试高精度数值
        String highPrecisionValue = "1.123456789012345678901234567890";
        BigDecimal bd = BigDecimalUtils.fromString(highPrecisionValue);
        String result = BigDecimalUtils.toString(bd);

        // 验证精度得到保持（在默认精度范围内）
        assertNotNull(result);
        assertTrue(result.contains("1.123456789"));

        // 测试往返转换
        BigDecimal roundTrip = BigDecimalUtils.fromString(result);
        assertEquals(bd, roundTrip);
    }

    @Test
    @DisplayName("测试从DepthData获取BigDecimal值")
    void testDepthDataBigDecimalExtraction() {
        // 创建测试用的DepthData
        MarketDataMsg.DepthData depthData = MarketDataMsg.DepthData.newBuilder()
                .setTp(0)
                .setPx("1.23456789")
                .setSz("1000000.50")
                .setCondition("A")
                .setOri("LP1")
                .setUqId("test-001")
                .build();

        // 测试价格提取
        BigDecimal price = BigDecimalUtils.getPriceFromDepthData(depthData);
        assertEquals(new BigDecimal("1.23456789"), price);

        // 测试数量提取
        BigDecimal size = BigDecimalUtils.getSizeFromDepthData(depthData);
        assertEquals(new BigDecimal("1000000.50"), size);

        // 测试null处理
        assertEquals(BigDecimal.ZERO, BigDecimalUtils.getPriceFromDepthData(null));
        assertEquals(BigDecimal.ZERO, BigDecimalUtils.getSizeFromDepthData(null));
    }

    @Test
    @DisplayName("测试从MarketData获取BigDecimal值")
    void testMarketDataBigDecimalExtraction() {
        // 创建测试用的MarketData
        MarketDataMsg.MarketData marketData = MarketDataMsg.MarketData.newBuilder()
                .setTimestamp(System.currentTimeMillis())
                .setSymbol("EURUSD")
                .setBid("1.08501234")
                .setOffer("1.08521234")
                .setMid("1.08511234")
                .build();

        // 测试bid价格提取
        BigDecimal bid = BigDecimalUtils.getBidFromMarketData(marketData);
        assertEquals(new BigDecimal("1.08501234"), bid);

        // 测试offer价格提取
        BigDecimal offer = BigDecimalUtils.getOfferFromMarketData(marketData);
        assertEquals(new BigDecimal("1.08521234"), offer);

        // 测试mid价格提取
        BigDecimal mid = BigDecimalUtils.getMidFromMarketData(marketData);
        assertEquals(new BigDecimal("1.08511234"), mid);

        // 测试null处理
        assertEquals(BigDecimal.ZERO, BigDecimalUtils.getBidFromMarketData(null));
        assertEquals(BigDecimal.ZERO, BigDecimalUtils.getOfferFromMarketData(null));
        assertEquals(BigDecimal.ZERO, BigDecimalUtils.getMidFromMarketData(null));
    }
}

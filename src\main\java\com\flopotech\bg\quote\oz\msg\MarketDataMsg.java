// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: MarketDataMsg.proto
// Protobuf Java Version: 4.31.1

package com.flopotech.bg.quote.oz.msg;

@com.google.protobuf.Generated
public final class MarketDataMsg {
  private MarketDataMsg() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 31,
      /* patch= */ 1,
      /* suffix= */ "",
      MarketDataMsg.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DepthDataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.flopotech.bg.quote.oz.msg.DepthData)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 价格类型: 0-bid, 1-offer
     * </pre>
     *
     * <code>int32 tp = 1;</code>
     * @return The tp.
     */
    int getTp();

    /**
     * <pre>
     * 价格 - 使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string px = 2;</code>
     * @return The px.
     */
    java.lang.String getPx();
    /**
     * <pre>
     * 价格 - 使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string px = 2;</code>
     * @return The bytes for px.
     */
    com.google.protobuf.ByteString
        getPxBytes();

    /**
     * <pre>
     * 可交易量 - 使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string sz = 3;</code>
     * @return The sz.
     */
    java.lang.String getSz();
    /**
     * <pre>
     * 可交易量 - 使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string sz = 3;</code>
     * @return The bytes for sz.
     */
    com.google.protobuf.ByteString
        getSzBytes();

    /**
     * <pre>
     * 报价条件: A = Tradeable Quote(可交易报价), I = Indicative Quote(参考报价)
     * </pre>
     *
     * <code>string condition = 4;</code>
     * @return The condition.
     */
    java.lang.String getCondition();
    /**
     * <pre>
     * 报价条件: A = Tradeable Quote(可交易报价), I = Indicative Quote(参考报价)
     * </pre>
     *
     * <code>string condition = 4;</code>
     * @return The bytes for condition.
     */
    com.google.protobuf.ByteString
        getConditionBytes();

    /**
     * <pre>
     * 发起者
     * </pre>
     *
     * <code>string ori = 5;</code>
     * @return The ori.
     */
    java.lang.String getOri();
    /**
     * <pre>
     * 发起者
     * </pre>
     *
     * <code>string ori = 5;</code>
     * @return The bytes for ori.
     */
    com.google.protobuf.ByteString
        getOriBytes();

    /**
     * <pre>
     * 报价单唯一标识
     * </pre>
     *
     * <code>string uqId = 6;</code>
     * @return The uqId.
     */
    java.lang.String getUqId();
    /**
     * <pre>
     * 报价单唯一标识
     * </pre>
     *
     * <code>string uqId = 6;</code>
     * @return The bytes for uqId.
     */
    com.google.protobuf.ByteString
        getUqIdBytes();
  }
  /**
   * <pre>
   * OZ深度数据条目
   * </pre>
   *
   * Protobuf type {@code com.flopotech.bg.quote.oz.msg.DepthData}
   */
  public static final class DepthData extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:com.flopotech.bg.quote.oz.msg.DepthData)
      DepthDataOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 31,
        /* patch= */ 1,
        /* suffix= */ "",
        DepthData.class.getName());
    }
    // Use DepthData.newBuilder() to construct.
    private DepthData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private DepthData() {
      px_ = "";
      sz_ = "";
      condition_ = "";
      ori_ = "";
      uqId_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.flopotech.bg.quote.oz.msg.MarketDataMsg.internal_static_com_flopotech_bg_quote_oz_msg_DepthData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.flopotech.bg.quote.oz.msg.MarketDataMsg.internal_static_com_flopotech_bg_quote_oz_msg_DepthData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.class, com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.Builder.class);
    }

    public static final int TP_FIELD_NUMBER = 1;
    private int tp_ = 0;
    /**
     * <pre>
     * 价格类型: 0-bid, 1-offer
     * </pre>
     *
     * <code>int32 tp = 1;</code>
     * @return The tp.
     */
    @java.lang.Override
    public int getTp() {
      return tp_;
    }

    public static final int PX_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object px_ = "";
    /**
     * <pre>
     * 价格 - 使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string px = 2;</code>
     * @return The px.
     */
    @java.lang.Override
    public java.lang.String getPx() {
      java.lang.Object ref = px_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        px_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 价格 - 使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string px = 2;</code>
     * @return The bytes for px.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPxBytes() {
      java.lang.Object ref = px_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        px_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SZ_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object sz_ = "";
    /**
     * <pre>
     * 可交易量 - 使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string sz = 3;</code>
     * @return The sz.
     */
    @java.lang.Override
    public java.lang.String getSz() {
      java.lang.Object ref = sz_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sz_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 可交易量 - 使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string sz = 3;</code>
     * @return The bytes for sz.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSzBytes() {
      java.lang.Object ref = sz_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sz_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CONDITION_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object condition_ = "";
    /**
     * <pre>
     * 报价条件: A = Tradeable Quote(可交易报价), I = Indicative Quote(参考报价)
     * </pre>
     *
     * <code>string condition = 4;</code>
     * @return The condition.
     */
    @java.lang.Override
    public java.lang.String getCondition() {
      java.lang.Object ref = condition_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        condition_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 报价条件: A = Tradeable Quote(可交易报价), I = Indicative Quote(参考报价)
     * </pre>
     *
     * <code>string condition = 4;</code>
     * @return The bytes for condition.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getConditionBytes() {
      java.lang.Object ref = condition_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        condition_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ORI_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object ori_ = "";
    /**
     * <pre>
     * 发起者
     * </pre>
     *
     * <code>string ori = 5;</code>
     * @return The ori.
     */
    @java.lang.Override
    public java.lang.String getOri() {
      java.lang.Object ref = ori_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        ori_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 发起者
     * </pre>
     *
     * <code>string ori = 5;</code>
     * @return The bytes for ori.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOriBytes() {
      java.lang.Object ref = ori_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ori_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int UQID_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private volatile java.lang.Object uqId_ = "";
    /**
     * <pre>
     * 报价单唯一标识
     * </pre>
     *
     * <code>string uqId = 6;</code>
     * @return The uqId.
     */
    @java.lang.Override
    public java.lang.String getUqId() {
      java.lang.Object ref = uqId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        uqId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 报价单唯一标识
     * </pre>
     *
     * <code>string uqId = 6;</code>
     * @return The bytes for uqId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUqIdBytes() {
      java.lang.Object ref = uqId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        uqId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (tp_ != 0) {
        output.writeInt32(1, tp_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(px_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, px_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(sz_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, sz_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(condition_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 4, condition_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(ori_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 5, ori_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(uqId_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 6, uqId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (tp_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, tp_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(px_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, px_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(sz_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, sz_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(condition_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(4, condition_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(ori_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(5, ori_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(uqId_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(6, uqId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData)) {
        return super.equals(obj);
      }
      com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData other = (com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData) obj;

      if (getTp()
          != other.getTp()) return false;
      if (!getPx()
          .equals(other.getPx())) return false;
      if (!getSz()
          .equals(other.getSz())) return false;
      if (!getCondition()
          .equals(other.getCondition())) return false;
      if (!getOri()
          .equals(other.getOri())) return false;
      if (!getUqId()
          .equals(other.getUqId())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TP_FIELD_NUMBER;
      hash = (53 * hash) + getTp();
      hash = (37 * hash) + PX_FIELD_NUMBER;
      hash = (53 * hash) + getPx().hashCode();
      hash = (37 * hash) + SZ_FIELD_NUMBER;
      hash = (53 * hash) + getSz().hashCode();
      hash = (37 * hash) + CONDITION_FIELD_NUMBER;
      hash = (53 * hash) + getCondition().hashCode();
      hash = (37 * hash) + ORI_FIELD_NUMBER;
      hash = (53 * hash) + getOri().hashCode();
      hash = (37 * hash) + UQID_FIELD_NUMBER;
      hash = (53 * hash) + getUqId().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * OZ深度数据条目
     * </pre>
     *
     * Protobuf type {@code com.flopotech.bg.quote.oz.msg.DepthData}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.flopotech.bg.quote.oz.msg.DepthData)
        com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthDataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.flopotech.bg.quote.oz.msg.MarketDataMsg.internal_static_com_flopotech_bg_quote_oz_msg_DepthData_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.flopotech.bg.quote.oz.msg.MarketDataMsg.internal_static_com_flopotech_bg_quote_oz_msg_DepthData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.class, com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.Builder.class);
      }

      // Construct using com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        tp_ = 0;
        px_ = "";
        sz_ = "";
        condition_ = "";
        ori_ = "";
        uqId_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.flopotech.bg.quote.oz.msg.MarketDataMsg.internal_static_com_flopotech_bg_quote_oz_msg_DepthData_descriptor;
      }

      @java.lang.Override
      public com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData getDefaultInstanceForType() {
        return com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.getDefaultInstance();
      }

      @java.lang.Override
      public com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData build() {
        com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData buildPartial() {
        com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData result = new com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.tp_ = tp_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.px_ = px_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.sz_ = sz_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.condition_ = condition_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.ori_ = ori_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.uqId_ = uqId_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData) {
          return mergeFrom((com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData other) {
        if (other == com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.getDefaultInstance()) return this;
        if (other.getTp() != 0) {
          setTp(other.getTp());
        }
        if (!other.getPx().isEmpty()) {
          px_ = other.px_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (!other.getSz().isEmpty()) {
          sz_ = other.sz_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (!other.getCondition().isEmpty()) {
          condition_ = other.condition_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (!other.getOri().isEmpty()) {
          ori_ = other.ori_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (!other.getUqId().isEmpty()) {
          uqId_ = other.uqId_;
          bitField0_ |= 0x00000020;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                tp_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                px_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                sz_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                condition_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                ori_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                uqId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int tp_ ;
      /**
       * <pre>
       * 价格类型: 0-bid, 1-offer
       * </pre>
       *
       * <code>int32 tp = 1;</code>
       * @return The tp.
       */
      @java.lang.Override
      public int getTp() {
        return tp_;
      }
      /**
       * <pre>
       * 价格类型: 0-bid, 1-offer
       * </pre>
       *
       * <code>int32 tp = 1;</code>
       * @param value The tp to set.
       * @return This builder for chaining.
       */
      public Builder setTp(int value) {

        tp_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 价格类型: 0-bid, 1-offer
       * </pre>
       *
       * <code>int32 tp = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTp() {
        bitField0_ = (bitField0_ & ~0x00000001);
        tp_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object px_ = "";
      /**
       * <pre>
       * 价格 - 使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string px = 2;</code>
       * @return The px.
       */
      public java.lang.String getPx() {
        java.lang.Object ref = px_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          px_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 价格 - 使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string px = 2;</code>
       * @return The bytes for px.
       */
      public com.google.protobuf.ByteString
          getPxBytes() {
        java.lang.Object ref = px_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          px_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 价格 - 使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string px = 2;</code>
       * @param value The px to set.
       * @return This builder for chaining.
       */
      public Builder setPx(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        px_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 价格 - 使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string px = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPx() {
        px_ = getDefaultInstance().getPx();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 价格 - 使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string px = 2;</code>
       * @param value The bytes for px to set.
       * @return This builder for chaining.
       */
      public Builder setPxBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        px_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private java.lang.Object sz_ = "";
      /**
       * <pre>
       * 可交易量 - 使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string sz = 3;</code>
       * @return The sz.
       */
      public java.lang.String getSz() {
        java.lang.Object ref = sz_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          sz_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 可交易量 - 使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string sz = 3;</code>
       * @return The bytes for sz.
       */
      public com.google.protobuf.ByteString
          getSzBytes() {
        java.lang.Object ref = sz_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sz_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 可交易量 - 使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string sz = 3;</code>
       * @param value The sz to set.
       * @return This builder for chaining.
       */
      public Builder setSz(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        sz_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可交易量 - 使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string sz = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSz() {
        sz_ = getDefaultInstance().getSz();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可交易量 - 使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string sz = 3;</code>
       * @param value The bytes for sz to set.
       * @return This builder for chaining.
       */
      public Builder setSzBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        sz_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private java.lang.Object condition_ = "";
      /**
       * <pre>
       * 报价条件: A = Tradeable Quote(可交易报价), I = Indicative Quote(参考报价)
       * </pre>
       *
       * <code>string condition = 4;</code>
       * @return The condition.
       */
      public java.lang.String getCondition() {
        java.lang.Object ref = condition_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          condition_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 报价条件: A = Tradeable Quote(可交易报价), I = Indicative Quote(参考报价)
       * </pre>
       *
       * <code>string condition = 4;</code>
       * @return The bytes for condition.
       */
      public com.google.protobuf.ByteString
          getConditionBytes() {
        java.lang.Object ref = condition_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          condition_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 报价条件: A = Tradeable Quote(可交易报价), I = Indicative Quote(参考报价)
       * </pre>
       *
       * <code>string condition = 4;</code>
       * @param value The condition to set.
       * @return This builder for chaining.
       */
      public Builder setCondition(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        condition_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 报价条件: A = Tradeable Quote(可交易报价), I = Indicative Quote(参考报价)
       * </pre>
       *
       * <code>string condition = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearCondition() {
        condition_ = getDefaultInstance().getCondition();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 报价条件: A = Tradeable Quote(可交易报价), I = Indicative Quote(参考报价)
       * </pre>
       *
       * <code>string condition = 4;</code>
       * @param value The bytes for condition to set.
       * @return This builder for chaining.
       */
      public Builder setConditionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        condition_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private java.lang.Object ori_ = "";
      /**
       * <pre>
       * 发起者
       * </pre>
       *
       * <code>string ori = 5;</code>
       * @return The ori.
       */
      public java.lang.String getOri() {
        java.lang.Object ref = ori_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          ori_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 发起者
       * </pre>
       *
       * <code>string ori = 5;</code>
       * @return The bytes for ori.
       */
      public com.google.protobuf.ByteString
          getOriBytes() {
        java.lang.Object ref = ori_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ori_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 发起者
       * </pre>
       *
       * <code>string ori = 5;</code>
       * @param value The ori to set.
       * @return This builder for chaining.
       */
      public Builder setOri(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ori_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 发起者
       * </pre>
       *
       * <code>string ori = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearOri() {
        ori_ = getDefaultInstance().getOri();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 发起者
       * </pre>
       *
       * <code>string ori = 5;</code>
       * @param value The bytes for ori to set.
       * @return This builder for chaining.
       */
      public Builder setOriBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        ori_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private java.lang.Object uqId_ = "";
      /**
       * <pre>
       * 报价单唯一标识
       * </pre>
       *
       * <code>string uqId = 6;</code>
       * @return The uqId.
       */
      public java.lang.String getUqId() {
        java.lang.Object ref = uqId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          uqId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 报价单唯一标识
       * </pre>
       *
       * <code>string uqId = 6;</code>
       * @return The bytes for uqId.
       */
      public com.google.protobuf.ByteString
          getUqIdBytes() {
        java.lang.Object ref = uqId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          uqId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 报价单唯一标识
       * </pre>
       *
       * <code>string uqId = 6;</code>
       * @param value The uqId to set.
       * @return This builder for chaining.
       */
      public Builder setUqId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        uqId_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 报价单唯一标识
       * </pre>
       *
       * <code>string uqId = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearUqId() {
        uqId_ = getDefaultInstance().getUqId();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 报价单唯一标识
       * </pre>
       *
       * <code>string uqId = 6;</code>
       * @param value The bytes for uqId to set.
       * @return This builder for chaining.
       */
      public Builder setUqIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        uqId_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:com.flopotech.bg.quote.oz.msg.DepthData)
    }

    // @@protoc_insertion_point(class_scope:com.flopotech.bg.quote.oz.msg.DepthData)
    private static final com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData();
    }

    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DepthData>
        PARSER = new com.google.protobuf.AbstractParser<DepthData>() {
      @java.lang.Override
      public DepthData parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<DepthData> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DepthData> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MarketDataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.flopotech.bg.quote.oz.msg.MarketData)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 时间戳 - 接收到OZ那边的发送时间，在fix标准请求头中
     * </pre>
     *
     * <code>int64 timestamp = 1;</code>
     * @return The timestamp.
     */
    long getTimestamp();

    /**
     * <pre>
     * bg-quote接收的时间 , 接收到行情到本地的时间
     * </pre>
     *
     * <code>int64 recvTime = 2;</code>
     * @return The recvTime.
     */
    long getRecvTime();

    /**
     * <pre>
     * 是否为超时数据 , 行情数据是否为旧数据 1-是 0-否
     * </pre>
     *
     * <code>int32 oldData = 3;</code>
     * @return The oldData.
     */
    int getOldData();

    /**
     * <pre>
     * 合约
     * </pre>
     *
     * <code>string symbol = 4;</code>
     * @return The symbol.
     */
    java.lang.String getSymbol();
    /**
     * <pre>
     * 合约
     * </pre>
     *
     * <code>string symbol = 4;</code>
     * @return The bytes for symbol.
     */
    com.google.protobuf.ByteString
        getSymbolBytes();

    /**
     * <pre>
     * 当前行情OZ的最优bid价 - OZ返回的MDEntryType=0，使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string bid = 5;</code>
     * @return The bid.
     */
    java.lang.String getBid();
    /**
     * <pre>
     * 当前行情OZ的最优bid价 - OZ返回的MDEntryType=0，使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string bid = 5;</code>
     * @return The bytes for bid.
     */
    com.google.protobuf.ByteString
        getBidBytes();

    /**
     * <pre>
     * 当前行情OZ的最优offer价 - OZ返回的MDEntryType=1，使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string offer = 6;</code>
     * @return The offer.
     */
    java.lang.String getOffer();
    /**
     * <pre>
     * 当前行情OZ的最优offer价 - OZ返回的MDEntryType=1，使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string offer = 6;</code>
     * @return The bytes for offer.
     */
    com.google.protobuf.ByteString
        getOfferBytes();

    /**
     * <pre>
     * 中间价 - (bid+offer)/2，使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string mid = 7;</code>
     * @return The mid.
     */
    java.lang.String getMid();
    /**
     * <pre>
     * 中间价 - (bid+offer)/2，使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string mid = 7;</code>
     * @return The bytes for mid.
     */
    com.google.protobuf.ByteString
        getMidBytes();

    /**
     * <pre>
     * OZ的深度数据集合
     * </pre>
     *
     * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
     */
    java.util.List<com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData> 
        getDataList();
    /**
     * <pre>
     * OZ的深度数据集合
     * </pre>
     *
     * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
     */
    com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData getData(int index);
    /**
     * <pre>
     * OZ的深度数据集合
     * </pre>
     *
     * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
     */
    int getDataCount();
    /**
     * <pre>
     * OZ的深度数据集合
     * </pre>
     *
     * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
     */
    java.util.List<? extends com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthDataOrBuilder> 
        getDataOrBuilderList();
    /**
     * <pre>
     * OZ的深度数据集合
     * </pre>
     *
     * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
     */
    com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthDataOrBuilder getDataOrBuilder(
        int index);
  }
  /**
   * <pre>
   * OZ报价消息
   * </pre>
   *
   * Protobuf type {@code com.flopotech.bg.quote.oz.msg.MarketData}
   */
  public static final class MarketData extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:com.flopotech.bg.quote.oz.msg.MarketData)
      MarketDataOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 31,
        /* patch= */ 1,
        /* suffix= */ "",
        MarketData.class.getName());
    }
    // Use MarketData.newBuilder() to construct.
    private MarketData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MarketData() {
      symbol_ = "";
      bid_ = "";
      offer_ = "";
      mid_ = "";
      data_ = java.util.Collections.emptyList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.flopotech.bg.quote.oz.msg.MarketDataMsg.internal_static_com_flopotech_bg_quote_oz_msg_MarketData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.flopotech.bg.quote.oz.msg.MarketDataMsg.internal_static_com_flopotech_bg_quote_oz_msg_MarketData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData.class, com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData.Builder.class);
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 1;
    private long timestamp_ = 0L;
    /**
     * <pre>
     * 时间戳 - 接收到OZ那边的发送时间，在fix标准请求头中
     * </pre>
     *
     * <code>int64 timestamp = 1;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public long getTimestamp() {
      return timestamp_;
    }

    public static final int RECVTIME_FIELD_NUMBER = 2;
    private long recvTime_ = 0L;
    /**
     * <pre>
     * bg-quote接收的时间 , 接收到行情到本地的时间
     * </pre>
     *
     * <code>int64 recvTime = 2;</code>
     * @return The recvTime.
     */
    @java.lang.Override
    public long getRecvTime() {
      return recvTime_;
    }

    public static final int OLDDATA_FIELD_NUMBER = 3;
    private int oldData_ = 0;
    /**
     * <pre>
     * 是否为超时数据 , 行情数据是否为旧数据 1-是 0-否
     * </pre>
     *
     * <code>int32 oldData = 3;</code>
     * @return The oldData.
     */
    @java.lang.Override
    public int getOldData() {
      return oldData_;
    }

    public static final int SYMBOL_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object symbol_ = "";
    /**
     * <pre>
     * 合约
     * </pre>
     *
     * <code>string symbol = 4;</code>
     * @return The symbol.
     */
    @java.lang.Override
    public java.lang.String getSymbol() {
      java.lang.Object ref = symbol_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        symbol_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 合约
     * </pre>
     *
     * <code>string symbol = 4;</code>
     * @return The bytes for symbol.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSymbolBytes() {
      java.lang.Object ref = symbol_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        symbol_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int BID_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object bid_ = "";
    /**
     * <pre>
     * 当前行情OZ的最优bid价 - OZ返回的MDEntryType=0，使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string bid = 5;</code>
     * @return The bid.
     */
    @java.lang.Override
    public java.lang.String getBid() {
      java.lang.Object ref = bid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        bid_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 当前行情OZ的最优bid价 - OZ返回的MDEntryType=0，使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string bid = 5;</code>
     * @return The bytes for bid.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBidBytes() {
      java.lang.Object ref = bid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        bid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OFFER_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private volatile java.lang.Object offer_ = "";
    /**
     * <pre>
     * 当前行情OZ的最优offer价 - OZ返回的MDEntryType=1，使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string offer = 6;</code>
     * @return The offer.
     */
    @java.lang.Override
    public java.lang.String getOffer() {
      java.lang.Object ref = offer_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        offer_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 当前行情OZ的最优offer价 - OZ返回的MDEntryType=1，使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string offer = 6;</code>
     * @return The bytes for offer.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOfferBytes() {
      java.lang.Object ref = offer_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        offer_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MID_FIELD_NUMBER = 7;
    @SuppressWarnings("serial")
    private volatile java.lang.Object mid_ = "";
    /**
     * <pre>
     * 中间价 - (bid+offer)/2，使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string mid = 7;</code>
     * @return The mid.
     */
    @java.lang.Override
    public java.lang.String getMid() {
      java.lang.Object ref = mid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        mid_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 中间价 - (bid+offer)/2，使用字符串存储BigDecimal以保证精度
     * </pre>
     *
     * <code>string mid = 7;</code>
     * @return The bytes for mid.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMidBytes() {
      java.lang.Object ref = mid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        mid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DATA_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private java.util.List<com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData> data_;
    /**
     * <pre>
     * OZ的深度数据集合
     * </pre>
     *
     * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
     */
    @java.lang.Override
    public java.util.List<com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData> getDataList() {
      return data_;
    }
    /**
     * <pre>
     * OZ的深度数据集合
     * </pre>
     *
     * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthDataOrBuilder> 
        getDataOrBuilderList() {
      return data_;
    }
    /**
     * <pre>
     * OZ的深度数据集合
     * </pre>
     *
     * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
     */
    @java.lang.Override
    public int getDataCount() {
      return data_.size();
    }
    /**
     * <pre>
     * OZ的深度数据集合
     * </pre>
     *
     * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
     */
    @java.lang.Override
    public com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData getData(int index) {
      return data_.get(index);
    }
    /**
     * <pre>
     * OZ的深度数据集合
     * </pre>
     *
     * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
     */
    @java.lang.Override
    public com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthDataOrBuilder getDataOrBuilder(
        int index) {
      return data_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (timestamp_ != 0L) {
        output.writeInt64(1, timestamp_);
      }
      if (recvTime_ != 0L) {
        output.writeInt64(2, recvTime_);
      }
      if (oldData_ != 0) {
        output.writeInt32(3, oldData_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(symbol_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 4, symbol_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bid_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 5, bid_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(offer_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 6, offer_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(mid_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 7, mid_);
      }
      for (int i = 0; i < data_.size(); i++) {
        output.writeMessage(8, data_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (timestamp_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, timestamp_);
      }
      if (recvTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, recvTime_);
      }
      if (oldData_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, oldData_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(symbol_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(4, symbol_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bid_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(5, bid_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(offer_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(6, offer_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(mid_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(7, mid_);
      }
      for (int i = 0; i < data_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, data_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData)) {
        return super.equals(obj);
      }
      com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData other = (com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData) obj;

      if (getTimestamp()
          != other.getTimestamp()) return false;
      if (getRecvTime()
          != other.getRecvTime()) return false;
      if (getOldData()
          != other.getOldData()) return false;
      if (!getSymbol()
          .equals(other.getSymbol())) return false;
      if (!getBid()
          .equals(other.getBid())) return false;
      if (!getOffer()
          .equals(other.getOffer())) return false;
      if (!getMid()
          .equals(other.getMid())) return false;
      if (!getDataList()
          .equals(other.getDataList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTimestamp());
      hash = (37 * hash) + RECVTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRecvTime());
      hash = (37 * hash) + OLDDATA_FIELD_NUMBER;
      hash = (53 * hash) + getOldData();
      hash = (37 * hash) + SYMBOL_FIELD_NUMBER;
      hash = (53 * hash) + getSymbol().hashCode();
      hash = (37 * hash) + BID_FIELD_NUMBER;
      hash = (53 * hash) + getBid().hashCode();
      hash = (37 * hash) + OFFER_FIELD_NUMBER;
      hash = (53 * hash) + getOffer().hashCode();
      hash = (37 * hash) + MID_FIELD_NUMBER;
      hash = (53 * hash) + getMid().hashCode();
      if (getDataCount() > 0) {
        hash = (37 * hash) + DATA_FIELD_NUMBER;
        hash = (53 * hash) + getDataList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * OZ报价消息
     * </pre>
     *
     * Protobuf type {@code com.flopotech.bg.quote.oz.msg.MarketData}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.flopotech.bg.quote.oz.msg.MarketData)
        com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketDataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.flopotech.bg.quote.oz.msg.MarketDataMsg.internal_static_com_flopotech_bg_quote_oz_msg_MarketData_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.flopotech.bg.quote.oz.msg.MarketDataMsg.internal_static_com_flopotech_bg_quote_oz_msg_MarketData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData.class, com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData.Builder.class);
      }

      // Construct using com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        timestamp_ = 0L;
        recvTime_ = 0L;
        oldData_ = 0;
        symbol_ = "";
        bid_ = "";
        offer_ = "";
        mid_ = "";
        if (dataBuilder_ == null) {
          data_ = java.util.Collections.emptyList();
        } else {
          data_ = null;
          dataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000080);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.flopotech.bg.quote.oz.msg.MarketDataMsg.internal_static_com_flopotech_bg_quote_oz_msg_MarketData_descriptor;
      }

      @java.lang.Override
      public com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData getDefaultInstanceForType() {
        return com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData.getDefaultInstance();
      }

      @java.lang.Override
      public com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData build() {
        com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData buildPartial() {
        com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData result = new com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData result) {
        if (dataBuilder_ == null) {
          if (((bitField0_ & 0x00000080) != 0)) {
            data_ = java.util.Collections.unmodifiableList(data_);
            bitField0_ = (bitField0_ & ~0x00000080);
          }
          result.data_ = data_;
        } else {
          result.data_ = dataBuilder_.build();
        }
      }

      private void buildPartial0(com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.timestamp_ = timestamp_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.recvTime_ = recvTime_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.oldData_ = oldData_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.symbol_ = symbol_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.bid_ = bid_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.offer_ = offer_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.mid_ = mid_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData) {
          return mergeFrom((com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData other) {
        if (other == com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData.getDefaultInstance()) return this;
        if (other.getTimestamp() != 0L) {
          setTimestamp(other.getTimestamp());
        }
        if (other.getRecvTime() != 0L) {
          setRecvTime(other.getRecvTime());
        }
        if (other.getOldData() != 0) {
          setOldData(other.getOldData());
        }
        if (!other.getSymbol().isEmpty()) {
          symbol_ = other.symbol_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (!other.getBid().isEmpty()) {
          bid_ = other.bid_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (!other.getOffer().isEmpty()) {
          offer_ = other.offer_;
          bitField0_ |= 0x00000020;
          onChanged();
        }
        if (!other.getMid().isEmpty()) {
          mid_ = other.mid_;
          bitField0_ |= 0x00000040;
          onChanged();
        }
        if (dataBuilder_ == null) {
          if (!other.data_.isEmpty()) {
            if (data_.isEmpty()) {
              data_ = other.data_;
              bitField0_ = (bitField0_ & ~0x00000080);
            } else {
              ensureDataIsMutable();
              data_.addAll(other.data_);
            }
            onChanged();
          }
        } else {
          if (!other.data_.isEmpty()) {
            if (dataBuilder_.isEmpty()) {
              dataBuilder_.dispose();
              dataBuilder_ = null;
              data_ = other.data_;
              bitField0_ = (bitField0_ & ~0x00000080);
              dataBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   internalGetDataFieldBuilder() : null;
            } else {
              dataBuilder_.addAllMessages(other.data_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                timestamp_ = input.readInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                recvTime_ = input.readInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                oldData_ = input.readInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                symbol_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                bid_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                offer_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 58: {
                mid_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000040;
                break;
              } // case 58
              case 66: {
                com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData m =
                    input.readMessage(
                        com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.parser(),
                        extensionRegistry);
                if (dataBuilder_ == null) {
                  ensureDataIsMutable();
                  data_.add(m);
                } else {
                  dataBuilder_.addMessage(m);
                }
                break;
              } // case 66
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long timestamp_ ;
      /**
       * <pre>
       * 时间戳 - 接收到OZ那边的发送时间，在fix标准请求头中
       * </pre>
       *
       * <code>int64 timestamp = 1;</code>
       * @return The timestamp.
       */
      @java.lang.Override
      public long getTimestamp() {
        return timestamp_;
      }
      /**
       * <pre>
       * 时间戳 - 接收到OZ那边的发送时间，在fix标准请求头中
       * </pre>
       *
       * <code>int64 timestamp = 1;</code>
       * @param value The timestamp to set.
       * @return This builder for chaining.
       */
      public Builder setTimestamp(long value) {

        timestamp_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 时间戳 - 接收到OZ那边的发送时间，在fix标准请求头中
       * </pre>
       *
       * <code>int64 timestamp = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000001);
        timestamp_ = 0L;
        onChanged();
        return this;
      }

      private long recvTime_ ;
      /**
       * <pre>
       * bg-quote接收的时间 , 接收到行情到本地的时间
       * </pre>
       *
       * <code>int64 recvTime = 2;</code>
       * @return The recvTime.
       */
      @java.lang.Override
      public long getRecvTime() {
        return recvTime_;
      }
      /**
       * <pre>
       * bg-quote接收的时间 , 接收到行情到本地的时间
       * </pre>
       *
       * <code>int64 recvTime = 2;</code>
       * @param value The recvTime to set.
       * @return This builder for chaining.
       */
      public Builder setRecvTime(long value) {

        recvTime_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * bg-quote接收的时间 , 接收到行情到本地的时间
       * </pre>
       *
       * <code>int64 recvTime = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRecvTime() {
        bitField0_ = (bitField0_ & ~0x00000002);
        recvTime_ = 0L;
        onChanged();
        return this;
      }

      private int oldData_ ;
      /**
       * <pre>
       * 是否为超时数据 , 行情数据是否为旧数据 1-是 0-否
       * </pre>
       *
       * <code>int32 oldData = 3;</code>
       * @return The oldData.
       */
      @java.lang.Override
      public int getOldData() {
        return oldData_;
      }
      /**
       * <pre>
       * 是否为超时数据 , 行情数据是否为旧数据 1-是 0-否
       * </pre>
       *
       * <code>int32 oldData = 3;</code>
       * @param value The oldData to set.
       * @return This builder for chaining.
       */
      public Builder setOldData(int value) {

        oldData_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否为超时数据 , 行情数据是否为旧数据 1-是 0-否
       * </pre>
       *
       * <code>int32 oldData = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearOldData() {
        bitField0_ = (bitField0_ & ~0x00000004);
        oldData_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object symbol_ = "";
      /**
       * <pre>
       * 合约
       * </pre>
       *
       * <code>string symbol = 4;</code>
       * @return The symbol.
       */
      public java.lang.String getSymbol() {
        java.lang.Object ref = symbol_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          symbol_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 合约
       * </pre>
       *
       * <code>string symbol = 4;</code>
       * @return The bytes for symbol.
       */
      public com.google.protobuf.ByteString
          getSymbolBytes() {
        java.lang.Object ref = symbol_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          symbol_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 合约
       * </pre>
       *
       * <code>string symbol = 4;</code>
       * @param value The symbol to set.
       * @return This builder for chaining.
       */
      public Builder setSymbol(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        symbol_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 合约
       * </pre>
       *
       * <code>string symbol = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearSymbol() {
        symbol_ = getDefaultInstance().getSymbol();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 合约
       * </pre>
       *
       * <code>string symbol = 4;</code>
       * @param value The bytes for symbol to set.
       * @return This builder for chaining.
       */
      public Builder setSymbolBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        symbol_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private java.lang.Object bid_ = "";
      /**
       * <pre>
       * 当前行情OZ的最优bid价 - OZ返回的MDEntryType=0，使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string bid = 5;</code>
       * @return The bid.
       */
      public java.lang.String getBid() {
        java.lang.Object ref = bid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          bid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 当前行情OZ的最优bid价 - OZ返回的MDEntryType=0，使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string bid = 5;</code>
       * @return The bytes for bid.
       */
      public com.google.protobuf.ByteString
          getBidBytes() {
        java.lang.Object ref = bid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          bid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 当前行情OZ的最优bid价 - OZ返回的MDEntryType=0，使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string bid = 5;</code>
       * @param value The bid to set.
       * @return This builder for chaining.
       */
      public Builder setBid(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        bid_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前行情OZ的最优bid价 - OZ返回的MDEntryType=0，使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string bid = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearBid() {
        bid_ = getDefaultInstance().getBid();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前行情OZ的最优bid价 - OZ返回的MDEntryType=0，使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string bid = 5;</code>
       * @param value The bytes for bid to set.
       * @return This builder for chaining.
       */
      public Builder setBidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        bid_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private java.lang.Object offer_ = "";
      /**
       * <pre>
       * 当前行情OZ的最优offer价 - OZ返回的MDEntryType=1，使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string offer = 6;</code>
       * @return The offer.
       */
      public java.lang.String getOffer() {
        java.lang.Object ref = offer_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          offer_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 当前行情OZ的最优offer价 - OZ返回的MDEntryType=1，使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string offer = 6;</code>
       * @return The bytes for offer.
       */
      public com.google.protobuf.ByteString
          getOfferBytes() {
        java.lang.Object ref = offer_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          offer_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 当前行情OZ的最优offer价 - OZ返回的MDEntryType=1，使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string offer = 6;</code>
       * @param value The offer to set.
       * @return This builder for chaining.
       */
      public Builder setOffer(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        offer_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前行情OZ的最优offer价 - OZ返回的MDEntryType=1，使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string offer = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearOffer() {
        offer_ = getDefaultInstance().getOffer();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前行情OZ的最优offer价 - OZ返回的MDEntryType=1，使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string offer = 6;</code>
       * @param value The bytes for offer to set.
       * @return This builder for chaining.
       */
      public Builder setOfferBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        offer_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }

      private java.lang.Object mid_ = "";
      /**
       * <pre>
       * 中间价 - (bid+offer)/2，使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string mid = 7;</code>
       * @return The mid.
       */
      public java.lang.String getMid() {
        java.lang.Object ref = mid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          mid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 中间价 - (bid+offer)/2，使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string mid = 7;</code>
       * @return The bytes for mid.
       */
      public com.google.protobuf.ByteString
          getMidBytes() {
        java.lang.Object ref = mid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          mid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 中间价 - (bid+offer)/2，使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string mid = 7;</code>
       * @param value The mid to set.
       * @return This builder for chaining.
       */
      public Builder setMid(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        mid_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 中间价 - (bid+offer)/2，使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string mid = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearMid() {
        mid_ = getDefaultInstance().getMid();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 中间价 - (bid+offer)/2，使用字符串存储BigDecimal以保证精度
       * </pre>
       *
       * <code>string mid = 7;</code>
       * @param value The bytes for mid to set.
       * @return This builder for chaining.
       */
      public Builder setMidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        mid_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }

      private java.util.List<com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData> data_ =
        java.util.Collections.emptyList();
      private void ensureDataIsMutable() {
        if (!((bitField0_ & 0x00000080) != 0)) {
          data_ = new java.util.ArrayList<com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData>(data_);
          bitField0_ |= 0x00000080;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData, com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.Builder, com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthDataOrBuilder> dataBuilder_;

      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public java.util.List<com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData> getDataList() {
        if (dataBuilder_ == null) {
          return java.util.Collections.unmodifiableList(data_);
        } else {
          return dataBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public int getDataCount() {
        if (dataBuilder_ == null) {
          return data_.size();
        } else {
          return dataBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData getData(int index) {
        if (dataBuilder_ == null) {
          return data_.get(index);
        } else {
          return dataBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public Builder setData(
          int index, com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.set(index, value);
          onChanged();
        } else {
          dataBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public Builder setData(
          int index, com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.set(index, builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public Builder addData(com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.add(value);
          onChanged();
        } else {
          dataBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public Builder addData(
          int index, com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.add(index, value);
          onChanged();
        } else {
          dataBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public Builder addData(
          com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.add(builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public Builder addData(
          int index, com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.add(index, builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public Builder addAllData(
          java.lang.Iterable<? extends com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData> values) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, data_);
          onChanged();
        } else {
          dataBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public Builder clearData() {
        if (dataBuilder_ == null) {
          data_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000080);
          onChanged();
        } else {
          dataBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public Builder removeData(int index) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.remove(index);
          onChanged();
        } else {
          dataBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.Builder getDataBuilder(
          int index) {
        return internalGetDataFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthDataOrBuilder getDataOrBuilder(
          int index) {
        if (dataBuilder_ == null) {
          return data_.get(index);  } else {
          return dataBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public java.util.List<? extends com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthDataOrBuilder> 
           getDataOrBuilderList() {
        if (dataBuilder_ != null) {
          return dataBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(data_);
        }
      }
      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.Builder addDataBuilder() {
        return internalGetDataFieldBuilder().addBuilder(
            com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.getDefaultInstance());
      }
      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.Builder addDataBuilder(
          int index) {
        return internalGetDataFieldBuilder().addBuilder(
            index, com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.getDefaultInstance());
      }
      /**
       * <pre>
       * OZ的深度数据集合
       * </pre>
       *
       * <code>repeated .com.flopotech.bg.quote.oz.msg.DepthData data = 8;</code>
       */
      public java.util.List<com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.Builder> 
           getDataBuilderList() {
        return internalGetDataFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData, com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.Builder, com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthDataOrBuilder> 
          internalGetDataFieldBuilder() {
        if (dataBuilder_ == null) {
          dataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData, com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData.Builder, com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthDataOrBuilder>(
                  data_,
                  ((bitField0_ & 0x00000080) != 0),
                  getParentForChildren(),
                  isClean());
          data_ = null;
        }
        return dataBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:com.flopotech.bg.quote.oz.msg.MarketData)
    }

    // @@protoc_insertion_point(class_scope:com.flopotech.bg.quote.oz.msg.MarketData)
    private static final com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData();
    }

    public static com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MarketData>
        PARSER = new com.google.protobuf.AbstractParser<MarketData>() {
      @java.lang.Override
      public MarketData parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MarketData> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MarketData> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_flopotech_bg_quote_oz_msg_DepthData_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_com_flopotech_bg_quote_oz_msg_DepthData_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_flopotech_bg_quote_oz_msg_MarketData_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_com_flopotech_bg_quote_oz_msg_MarketData_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\023MarketDataMsg.proto\022\035com.flopotech.bg." +
      "quote.oz.msg\"]\n\tDepthData\022\n\n\002tp\030\001 \001(\005\022\n\n" +
      "\002px\030\002 \001(\t\022\n\n\002sz\030\003 \001(\t\022\021\n\tcondition\030\004 \001(\t" +
      "\022\013\n\003ori\030\005 \001(\t\022\014\n\004uqId\030\006 \001(\t\"\263\001\n\nMarketDa" +
      "ta\022\021\n\ttimestamp\030\001 \001(\003\022\020\n\010recvTime\030\002 \001(\003\022" +
      "\017\n\007oldData\030\003 \001(\005\022\016\n\006symbol\030\004 \001(\t\022\013\n\003bid\030" +
      "\005 \001(\t\022\r\n\005offer\030\006 \001(\t\022\013\n\003mid\030\007 \001(\t\0226\n\004dat" +
      "a\030\010 \003(\0132(.com.flopotech.bg.quote.oz.msg." +
      "DepthDatab\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_flopotech_bg_quote_oz_msg_DepthData_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_flopotech_bg_quote_oz_msg_DepthData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_com_flopotech_bg_quote_oz_msg_DepthData_descriptor,
        new java.lang.String[] { "Tp", "Px", "Sz", "Condition", "Ori", "UqId", });
    internal_static_com_flopotech_bg_quote_oz_msg_MarketData_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_flopotech_bg_quote_oz_msg_MarketData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_com_flopotech_bg_quote_oz_msg_MarketData_descriptor,
        new java.lang.String[] { "Timestamp", "RecvTime", "OldData", "Symbol", "Bid", "Offer", "Mid", "Data", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

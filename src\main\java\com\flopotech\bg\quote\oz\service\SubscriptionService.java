package com.flopotech.bg.quote.oz.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import quickfix.FieldNotFound;
import quickfix.Session;
import quickfix.SessionID;
import quickfix.SessionNotFound;
import quickfix.field.*;
import quickfix.fix44.MarketDataRequest;
import quickfix.fix44.MarketDataRequestReject;
import quickfix.fix44.MarketDataSnapshotFullRefresh;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


@Service
public class SubscriptionService {

    private static final Logger logger = LoggerFactory.getLogger(SubscriptionService.class);

    // 状态集合
    private final Map<String, SymbolSubConfig> pendingSubscriptions = new ConcurrentHashMap<>();
    private final Map<String, SymbolSubConfig> requestFailedSubscriptions = new ConcurrentHashMap<>();
    private final Map<String, SymbolSubConfig> subscriptionFailedSymbols = new ConcurrentHashMap<>();
    private final Map<String, SymbolSubConfig> successfulSubscriptions = new ConcurrentHashMap<>();

    private final Map<String, SymbolSubConfig> errorFailedSymbols = new ConcurrentHashMap<>();

    @Value("${quickfix.onezero.subscribe.symbol}")
    private String subSymbol;
    @Value("${quickfix.onezero.subscribe.symbol.maxretries}")
    private int maxRetries;

    public void subscribeAll(SessionID sessionID) {
        Arrays.stream(subSymbol.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .forEach(symbol -> {
                    try {
                        subscribe(sessionID, symbol);
                    } catch (Exception e) {
                        logger.error("Failed to subscribe symbol: {}", symbol, e);
                    }
                });
    }

    // ========== 核心订阅方法 ==========

    public boolean subscribe(SessionID sessionID, String symbol) {
        // 检查是否已经在 pending 或 failed 状态
        // 检查 pending 状态时直接查 Map
        if (pendingSubscriptions.containsKey(symbol)) {
            logger.debug("Symbol {} is already in pending/failed state", symbol);
            return false;
        }
        if (successfulSubscriptions.containsKey(symbol)) {
            logger.debug("Symbol {} already subscribed", symbol);
            return true;
        }

        String mdReqID = "MD_REQ_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
        SymbolSubConfig config = requestFailedSubscriptions.get(symbol);
        if (config == null) {
            config = new SymbolSubConfig(sessionID, symbol);
        }
        config.setMdReqID(mdReqID);
        try {
            MarketDataRequest request = createSubscriptionRequest(mdReqID, symbol);
            Session.sendToTarget(request, sessionID);
            pendingSubscriptions.put(symbol, config);
            if (requestFailedSubscriptions.containsKey(symbol)) {
                requestFailedSubscriptions.remove(symbol);
            }
            return true;
        } catch (SessionNotFound e) {
            requestFailedSubscriptions.put(symbol, config);
            return false;
        } catch (Exception e) {
            requestFailedSubscriptions.put(symbol, config);
            return false;
        }
    }


    private MarketDataRequest createSubscriptionRequest(String mdReqID, String symbol) {
        MarketDataRequest request = new MarketDataRequest();
        request.set(new MDReqID(mdReqID));
        request.set(new SubscriptionRequestType('1'));
        request.set(new MarketDepth(5));
        request.set(new MDUpdateType(MDUpdateType.FULL_REFRESH));

        // 添加要订阅的数据类型
        MarketDataRequest.NoMDEntryTypes entryTypesGroup = new MarketDataRequest.NoMDEntryTypes();
        entryTypesGroup.set(new MDEntryType(MDEntryType.BID)); // 买价
        request.addGroup(entryTypesGroup);

        entryTypesGroup = new MarketDataRequest.NoMDEntryTypes();
        entryTypesGroup.set(new MDEntryType(MDEntryType.OFFER)); // 卖价
        request.addGroup(entryTypesGroup);
        // Add symbol
        MarketDataRequest.NoRelatedSym symbolGroup = new MarketDataRequest.NoRelatedSym();
        symbolGroup.set(new Symbol(symbol));
        request.addGroup(symbolGroup);

        return request;
    }


    public void handleSubscriptionReject(MarketDataRequestReject reject) throws FieldNotFound {
        String mdReqID = reject.getMDReqID().getValue();
        Optional<String> keyOpt = pendingSubscriptions.entrySet()
                .stream()
                .filter(entry -> mdReqID.equals(entry.getValue().getMDReqID()))
                .map(Map.Entry::getKey)
                .findFirst();
        if (keyOpt.isPresent()) {
            String key = keyOpt.get();
            SymbolSubConfig config = pendingSubscriptions.remove(key);
            if (config != null) {
                MDReqRejReason reason = reject.getMDReqRejReason();
                char reasonCode = reason.getValue();
                if (reasonCode == MDReqRejReason.UNKNOWN_SYMBOL||reasonCode == MDReqRejReason.INSUFFICIENT_PERMISSIONS) {
                    errorFailedSymbols.put(key, config);
                    return;
                }
                //                    String text = reject.isSetText() ? reject.getText().getValue() : "No reason";
                logger.warn("Subscription rejected for symbol: {}, reason: {}", config.getSymbol(), reason.getValue());
                if (!subscriptionFailedSymbols.containsKey(key)) {
                    subscriptionFailedSymbols.put(config.getSymbol(), config);
                }
            }
        }

    }


    public void handleSubscriptionSuccess(MarketDataSnapshotFullRefresh message) {
        try {
            String mdReqID = message.getMDReqID().getValue();
            Optional<String> keyOpt = pendingSubscriptions.entrySet()
                    .stream()
                    .filter(entry -> mdReqID.equals(entry.getValue().getMDReqID()))
                    .map(Map.Entry::getKey)
                    .findFirst();
            if (keyOpt.isPresent()) {
                String key = keyOpt.get();
                SymbolSubConfig config = pendingSubscriptions.remove(key);
                if (config != null) {
                    if (!successfulSubscriptions.containsKey(key)) {
                        successfulSubscriptions.put(config.getSymbol(), config);
                    }
                    subscriptionFailedSymbols.remove(key);
                }
            }
        } catch (FieldNotFound e) {
            logger.error("Failed to process subscription success message", e);
        }
    }


    @Scheduled(fixedDelay = 3000)
    public void retryFailedSubscriptions() {
        logger.info("Starting retry - Request failures: {}, Subscription failures: {} error failures: {}",
                requestFailedSubscriptions.size(), subscriptionFailedSymbols.size(),errorFailedSymbols.size());
        logger.info("Starting retry - Request pending: {}, Subscription success: {}",
                pendingSubscriptions.size(),successfulSubscriptions.size());
        if (requestFailedSubscriptions.isEmpty() && subscriptionFailedSymbols.isEmpty()) {
            return;
        }
        if (!requestFailedSubscriptions.isEmpty()) {
            Iterator<Map.Entry<String, SymbolSubConfig>> reqFailediterator = requestFailedSubscriptions.entrySet().iterator();
            while (reqFailediterator.hasNext()) {
                Map.Entry<String, SymbolSubConfig> entry = reqFailediterator.next();
                SymbolSubConfig config = entry.getValue();
                if (config.getCurrentReqRetry() >= maxRetries) {
                    reqFailediterator.remove(); // 使用迭代器安全删除
                    errorFailedSymbols.put(config.getSymbol(), config);
                    continue;
                }
                config.incrementReqRetry();
                subscribe(config.getSessionID(), config.getSymbol());
            }
        }

        if (!subscriptionFailedSymbols.isEmpty()) {
            Iterator<Map.Entry<String, SymbolSubConfig>> subFailediterator = subscriptionFailedSymbols.entrySet().iterator();
            while (subFailediterator.hasNext()) {
                Map.Entry<String, SymbolSubConfig> entry = subFailediterator.next();
                SymbolSubConfig config = entry.getValue();
                if (config.getCurrentSubRetry() >= maxRetries) {
                    subFailediterator.remove(); // 使用迭代器安全删除
                    errorFailedSymbols.put(config.getSymbol(), config);
                    continue;
                }
                config.incrementSubRetry();
                subscribe(config.getSessionID(), config.getSymbol());
            }
        }
        logger.info("retry end - Request failures: {}, Subscription failures: {} Request pending: {}, Subscription success: {}",
                requestFailedSubscriptions.size(), subscriptionFailedSymbols.size(),pendingSubscriptions.size(),successfulSubscriptions.size());
    }

    // ========== Response Handlers ==========


    public Collection<SymbolSubConfig> getPendingSubscriptions() {
        return new ArrayList<>(pendingSubscriptions.values());
    }


    public Collection<SymbolSubConfig> getRequestFailedSubscriptions() {
        return new ArrayList<>(requestFailedSubscriptions.values());
    }


    public Collection<SymbolSubConfig> getSubscriptionFailedSymbols() {
        return new ArrayList<>(subscriptionFailedSymbols.values());
    }


    public Collection<SymbolSubConfig> getSuccessfulSubscriptions() {
        return new ArrayList<>(successfulSubscriptions.values());
    }


    public SymbolSubConfig getSubConfig(SessionID sessionID,String symbol) {
        if (pendingSubscriptions.containsKey(symbol)) {
            SymbolSubConfig  config = pendingSubscriptions.get(symbol);
            config.setStatus("PENDING");
            return config;
        }
        if (requestFailedSubscriptions.containsKey(symbol)) {
            SymbolSubConfig  config = requestFailedSubscriptions.get(symbol);
            config.setStatus("REQUEST_FAILED");
            return config;
        }
        if (subscriptionFailedSymbols.containsKey(symbol)) {
            SymbolSubConfig  config = subscriptionFailedSymbols.get(symbol);
            config.setStatus("SUBSCRIPTION_FAILED");
            return config;
        }
        if (successfulSubscriptions.containsKey(symbol)) {
            SymbolSubConfig  config = successfulSubscriptions.get(symbol);
            config.setStatus("SUCCESS");
            return config;
        }
        return null;
    }

    public Boolean getSubFlag(SessionID sessionID,String symbol) {
        if (pendingSubscriptions.containsKey(symbol)) {
            return true;
        }
        if (requestFailedSubscriptions.containsKey(symbol)) {
            return true;
        }
        if (subscriptionFailedSymbols.containsKey(symbol)) {
            return true;
        }
        if (successfulSubscriptions.containsKey(symbol)) {
            return true;
        }
        return false;
    }

    /**
     * 合约配置模型：存储需要订阅的合约信息
     */
    public static class SymbolSubConfig {

        private SessionID sessionID;
        private String mdReqID;
        private final String symbol;
        private int currentReqRetry;
        private int currentSubRetry;
        private long lastAttemptTime;
        private String status;

        public SessionID getSessionID() {
            return sessionID;
        }

        public SymbolSubConfig(SessionID sessionID, String symbol) {
            this.sessionID = sessionID;
            this.symbol = symbol;
            this.currentReqRetry = 0;
            this.currentSubRetry = 0;
            this.lastAttemptTime = System.currentTimeMillis();
        }

        public void incrementReqRetry() {
            this.currentReqRetry++;
            this.lastAttemptTime = System.currentTimeMillis();
        }

        public void incrementSubRetry() {
            this.currentSubRetry++;
            this.lastAttemptTime = System.currentTimeMillis();
        }

        // Getters and Setters
        public String getMDReqID() {
            return mdReqID;
        }

        public String getSymbol() {
            return symbol;
        }

        public void setMdReqID(String mdReqID) {
            this.mdReqID = mdReqID;
        }

        public int getCurrentReqRetry() {
            return currentReqRetry;
        }

        public int getCurrentSubRetry() {
            return currentSubRetry;
        }

        public void setCurrentReqRetry(int currentReqRetry) {
            this.currentReqRetry = currentReqRetry;
        }

        public void setCurrentSubRetry(int currentSubRetry) {
            this.currentSubRetry = currentSubRetry;
        }

        public long getLastAttemptTime() {
            return lastAttemptTime;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        @Override
        public String toString() {
            return "SymbolSubConfig{" +
                    "symbol='" + symbol + '\'' +
                    ", retry=" + currentReqRetry +
                    ", lastAttempt=" + lastAttemptTime;
        }
    }

}
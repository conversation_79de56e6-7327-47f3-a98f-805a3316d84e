spring.application.name=bg-oz-quote

logging.config=classpath:logback-spring.xml
logging.level.com.fudianyun=DEBUG
logging.level.org.springframework=INFO
logging.level.org.apache=WARN
logging.file.path=logs
logging.charset.console=UTF-8
logging.charset.file=UTF-8
logstash.address=*************:5044

quickfix.client.config=classpath:quickfix-client.cfg
quickfix.log.enabled=true
quickfix.log.path=logs/quickfix

quickfix.onezero.auto-start=true
quickfix.onezero.password=44wzBb4N6BDq
quickfix.onezero.subscribe.symbol=GBPUSD
quickfix.onezero.subscribe.symbol.maxretries=3


spring.kafka.bootstrap-servers=*************:9093

spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.ByteArraySerializer
spring.kafka.producer.acks=0
spring.kafka.producer.retries=2147483647
spring.kafka.producer.max-in-flight-requests-per-connection=1
spring.kafka.producer.enable-idempotence=false
spring.kafka.producer.batch-size=65536
spring.kafka.producer.linger-ms=10
spring.kafka.producer.buffer-memory=67108864
spring.kafka.producer.compression-type=lz4
spring.kafka.producer.request-timeout-ms=30000
spring.kafka.producer.delivery-timeout-ms=120000

spring.kafka.consumer.group-id=bg-oz-quote-group
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.ByteArrayDeserializer
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.enable-auto-commit=false
spring.kafka.consumer.fetch-min-bytes=50000
spring.kafka.consumer.fetch-max-wait=500
spring.kafka.consumer.max-poll-records=1000
spring.kafka.consumer.session-timeout-ms=30000
spring.kafka.consumer.heartbeat-interval-ms=10000
spring.kafka.consumer.max-poll-interval-ms=300000

kafka.topic.market-data=bg-quote-topic
kafka.topic.partitions=3
kafka.topic.replication-factor=1

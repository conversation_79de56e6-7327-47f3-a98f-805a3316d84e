package com.flopotech.bg.quote.oz.queue;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import com.flopotech.bg.quote.oz.service.MarketDataProducer;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Component
public class NioDirectMemoryQueue<K> {

    private static final Logger logger = LoggerFactory.getLogger(NioDirectMemoryQueue.class);

    @Autowired
    private MarketDataProducer marketDataProducer;

    private final Map<K, BlockingQueue<MarketDataMsg.MarketData>> rawQueues = new ConcurrentHashMap<>();
    private final Map<K, PriorityBlockingQueue<MarketDataMsg.MarketData>> sortedQueues = new ConcurrentHashMap<>();
    // 正序排序（最早的时间戳在前）
    private static final Comparator<MarketDataMsg.MarketData> MARKET_DATA_COMPARATOR = (md1, md2) -> Long
            .compare(md1.getTimestamp(), md2.getTimestamp());
    private final Map<K, ByteBuffer> directBuffers = new ConcurrentHashMap<>();
    private static final int BUFFER_SIZE = 1024;

    // 行情数据处理配置 - 针对高频数据优化
    private static final int BATCH_SIZE = 10; // 小批量处理，避免阻塞
    private static final int PROCESS_INTERVAL_MS = 5; // 5ms检查间隔，提高实时性
    private static final int EMPTY_QUEUE_WAIT_MS = 20; // 空队列等待时间
    private final ThreadPoolExecutor executorService;
    private volatile boolean running = true;

    public NioDirectMemoryQueue() {
        int corePoolSize = Runtime.getRuntime().availableProcessors() * 2;
        int maxPoolSize = Math.max(corePoolSize * 2, 200);
        executorService = new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                new ThreadPoolExecutor.CallerRunsPolicy());
        logger.debug("初始化线程池: 核心线程数={}, 最大线程数={}, 工作队列容量={}",
                corePoolSize, maxPoolSize, 1000);
    }

    @PostConstruct
    public void startProcessing() {
        int shards = Math.min(20, Runtime.getRuntime().availableProcessors() * 2);
        logger.info("启动 {} 个分片处理线程", shards);
        for (int i = 0; i < shards; i++) {
            final int shardIndex = i;
            executorService.submit(() -> processShard(shardIndex, shards));
        }
    }

    private void processShard(int shardIndex, int totalShards) {
        logger.debug("分片 {} 启动，处理线程: {}", shardIndex, Thread.currentThread().getName());
        while (running) {
            try {
                // 记录sortedQueues的总键数
                logger.debug("分片 {}: sortedQueues总键数={}", shardIndex, sortedQueues.size());

                // 优化分片逻辑，确保每个分片都能处理键
                List<K> allKeys = new ArrayList<>(sortedQueues.keySet());
                List<K> keys = new ArrayList<>();

                for (int i = 0; i < allKeys.size(); i++) {
                    if (i % totalShards == shardIndex) {
                        keys.add(allKeys.get(i));
                    }
                }

                if (keys.isEmpty()) {
                    // 如果当前分片没有键，尝试处理所有键的一小部分
                    if (!allKeys.isEmpty()) {
                        int start = shardIndex % allKeys.size();
                        int end = Math.min(start + 1, allKeys.size());
                        keys = allKeys.subList(start, end);
                        logger.debug("分片 {}: 没有分配到键，处理额外键: {}", shardIndex, keys);
                    } else {
                        Thread.sleep(EMPTY_QUEUE_WAIT_MS);
                        continue;
                    }
                }
                logger.debug("分片 {} 处理 {} 个 symbol", shardIndex, keys.size());
                for (K key : keys) {
                    processAndSendToKafka(key);
                }
                logger.debug("线程池状态: 活跃线程数={}, 队列大小={}, 已完成任务数={}",
                        executorService.getActiveCount(), executorService.getQueue().size(),
                        executorService.getCompletedTaskCount());
                Thread.sleep(PROCESS_INTERVAL_MS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.warn("分片 {} 处理线程被中断", shardIndex);
                break;
            } catch (Exception e) {
                logger.error("分片 {} 处理队列时发生错误: {}", shardIndex, e.getMessage(), e);
            }
        }
        logger.debug("分片 {} 处理线程已停止", shardIndex);
    }

    @PreDestroy
    public void shutdown() {
        running = false;
        logger.debug("正在关闭 NioDirectMemoryQueue，停止所有处理线程");
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                logger.warn("线程池未在 5 秒内正常关闭，强制终止");
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            logger.error("线程池关闭过程中被中断", e);
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
        logger.debug("NioDirectMemoryQueue 已关闭，剩余队列: {}", sortedQueues.size());
    }

    public void enqueue(K key, MarketDataMsg.MarketData value) throws InterruptedException, IOException {
        rawQueues.computeIfAbsent(key, k -> new LinkedBlockingQueue<>());
        sortedQueues.computeIfAbsent(key, k -> new PriorityBlockingQueue<>(
                1000, MARKET_DATA_COMPARATOR));
        ByteBuffer buffer = directBuffers.computeIfAbsent(key, k -> {
            int size = Math.max(BUFFER_SIZE, value.getSerializedSize());
            logger.debug("分配堆外内存: Key={}, 缓冲区大小={}", key, size);
            return ByteBuffer.allocateDirect(size);
        });

        buffer.clear();
        value.writeTo(new ByteBufferOutputStream(buffer)); // 直接写入 ByteBuffer
        buffer.flip();
        rawQueues.get(key).put(value);
        logger.info("入队: Key={}, Symbol={}, Timestamp={}",
                key, value.getSymbol(), value.getTimestamp());

        PriorityBlockingQueue<MarketDataMsg.MarketData> queue = sortedQueues.get(key);
        if (queue.size() > 1000) {
            logger.warn("队列积压: Key={}, 当前大小={}, 可能需要优化线程池或 Kafka 配置",
                    key, queue.size());
        }

        this.processToSortedQueue(key);
    }

    public void processToSortedQueue(K key) throws InterruptedException {
        BlockingQueue<MarketDataMsg.MarketData> rawQueue = rawQueues.get(key);
        PriorityBlockingQueue<MarketDataMsg.MarketData> sortedQueue = sortedQueues.get(key);

        if (rawQueue == null || sortedQueue == null) {
            logger.warn("队列不存在: Key={}", key);
            return;
        }

        // 处理所有可用的原始数据
        MarketDataMsg.MarketData item;
        while ((item = rawQueue.poll()) != null) {
            logger.info("排序: Key={}, Symbol={}, Timestamp={}",
                    key, item.getSymbol(), item.getTimestamp());
            sortedQueue.offer(item);
        }
    }

    private void processAndSendToKafka(K key) throws InterruptedException {
        PriorityBlockingQueue<MarketDataMsg.MarketData> sortedQueue = sortedQueues.get(key);
        ByteBuffer buffer = directBuffers.get(key);
        if (sortedQueue == null || sortedQueue.isEmpty() || buffer == null) {
            return;
        }

        List<MarketDataMsg.MarketData> batch = new ArrayList<>();
        // 行情数据频繁，使用小批量避免阻塞
        sortedQueue.drainTo(batch, BATCH_SIZE);
        if (batch.isEmpty()) {
            return;
        }

        logger.info("出队: Key={}, 批量大小={}", key, batch.size());
        for (MarketDataMsg.MarketData item : batch) {
            logger.info("处理数据: Key={}, Symbol={}, Timestamp={}",
                    key, item.getSymbol(), item.getTimestamp());
            try {
                buffer.clear();
                item.writeTo(new ByteBufferOutputStream(buffer));
                buffer.flip();
                marketDataProducer.sendMarketData(item, buffer); // 使用 ByteBuffer 发送
                logger.info("Kafka 发送成功: Key={}, Symbol={}, Timestamp={}",
                        key, item.getSymbol(), item.getTimestamp());
            } catch (Exception e) {
                logger.error("Kafka 发送失败: Key={}, Symbol={}, Timestamp={}, 错误={}",
                        key, item.getSymbol(), item.getTimestamp(), e.getMessage(), e);
            }
        }

        long directMemoryUsed = directBuffers.size() * BUFFER_SIZE;
        logger.debug("堆外内存使用: {} 字节 ({} 个 ByteBuffer)", directMemoryUsed, directBuffers.size());
    }

    public MarketDataMsg.MarketData dequeue(K key) throws InterruptedException {
        PriorityBlockingQueue<MarketDataMsg.MarketData> queue = sortedQueues.get(key);
        if (queue == null || queue.isEmpty()) {
            logger.debug("队列为空: Key={}", key);
            return null;
        }
        MarketDataMsg.MarketData item = queue.poll();
        if (item != null) {
            logger.info("手动出队: Key={}, Symbol={}, Timestamp={}",
                    key, item.getSymbol(), item.getTimestamp());
        }
        return item;
    }

    public boolean isEmpty(K key) {
        PriorityBlockingQueue<MarketDataMsg.MarketData> queue = sortedQueues.get(key);
        boolean isEmpty = queue == null || queue.isEmpty();
        logger.debug("检查队列状态: Key={}, 是否为空={}", key, isEmpty);
        return isEmpty;
    }

    // 辅助类：将 Protobuf 写入 ByteBuffer
    private static class ByteBufferOutputStream extends OutputStream {
        private final ByteBuffer buffer;

        public ByteBufferOutputStream(ByteBuffer buffer) {
            this.buffer = buffer;
        }

        @Override
        public void write(int b) throws IOException {
            buffer.put((byte) b);
        }

        @Override
        public void write(byte[] bytes, int off, int len) throws IOException {
            buffer.put(bytes, off, len);
        }
    }

    public static void main(String[] args) throws Exception {
        System.out.println(System.currentTimeMillis());
        NioDirectMemoryQueue<String> queue = new NioDirectMemoryQueue<>();
        for (int i = 0; i < 200; i++) {
            String symbol = "SYMBOL" + i;
            for (int j = 0; j < 100; j++) {

                MarketDataMsg.MarketData data = MarketDataMsg.MarketData.newBuilder()
                        .setTimestamp(System.currentTimeMillis() + j)
                        .setSymbol(symbol)
                        .build();
                queue.enqueue(symbol, data);
            }
        }

        logger.info("SYMBOL0 队列是否为空: {}", queue.isEmpty("SYMBOL0"));
        logger.info("SYMBOL199 队列是否为空: {}", queue.isEmpty("SYMBOL199"));
        System.out.println(System.currentTimeMillis());
    }
}
# Kafka配置分离说明

## 概述

为了更好的代码组织和维护，我们将Kafka配置分离为三个独立的配置类：

1. **KafkaConfig** - 通用配置（Admin、Topic管理）
2. **KafkaProducerConfig** - 生产者专用配置
3. **KafkaConsumerConfig** - 消费者专用配置

## 配置类结构

### 1. KafkaConfig.java
**职责**: Kafka Admin和Topic管理
```java
@Configuration
public class KafkaConfig {
    // Kafka Admin配置
    @Bean
    public KafkaAdmin kafkaAdmin()
    
    // Topic创建配置
    @Bean
    public NewTopic marketDataTopic()
}
```

**管理的配置**:
- Kafka Admin客户端
- Topic创建和管理
- 集群连接配置

### 2. KafkaProducerConfig.java
**职责**: 生产者相关配置
```java
@Configuration
public class KafkaProducerConfig {
    // Producer工厂配置
    @Bean
    public ProducerFactory<String, byte[]> producerFactory()
    
    // KafkaTemplate配置
    @Bean
    public KafkaTemplate<String, byte[]> kafkaTemplate()
}
```

**管理的配置**:
- Producer工厂
- KafkaTemplate
- 序列化器配置
- 可靠性配置（acks、retries、idempotence）
- 性能配置（batch-size、linger-ms、compression）
- 自定义分区器

### 3. KafkaConsumerConfig.java
**职责**: 消费者相关配置
```java
@Configuration
@EnableKafka
public class KafkaConsumerConfig {
    // Consumer工厂配置
    @Bean
    public ConsumerFactory<String, byte[]> consumerFactory()
    
    // Listener容器工厂配置
    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, byte[]> kafkaListenerContainerFactory()
}
```

**管理的配置**:
- Consumer工厂
- Listener容器工厂
- 反序列化器配置
- 消费配置（fetch、poll、session）
- 并发配置
- 错误处理配置

## 配置参数映射

### Producer配置参数
```properties
# 基础配置
spring.kafka.bootstrap-servers=localhost:9092

# 可靠性配置
spring.kafka.producer.acks=all
spring.kafka.producer.retries=2147483647
spring.kafka.producer.max-in-flight-requests-per-connection=1
spring.kafka.producer.enable-idempotence=true

# 性能配置
spring.kafka.producer.batch-size=65536
spring.kafka.producer.linger-ms=10
spring.kafka.producer.buffer-memory=67108864
spring.kafka.producer.compression-type=lz4

# 超时配置
spring.kafka.producer.request-timeout-ms=30000
spring.kafka.producer.delivery-timeout-ms=120000
```

### Consumer配置参数
```properties
# 基础配置
spring.kafka.consumer.group-id=bg-oz-quote-group

# 偏移量配置
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.enable-auto-commit=false

# 性能配置
spring.kafka.consumer.fetch-min-bytes=50000
spring.kafka.consumer.fetch-max-wait=500
spring.kafka.consumer.max-poll-records=1000

# 会话配置
spring.kafka.consumer.session-timeout-ms=30000
spring.kafka.consumer.heartbeat-interval-ms=10000
spring.kafka.consumer.max-poll-interval-ms=300000
```

### Topic配置参数
```properties
# Topic配置
kafka.topic.market-data=market-data-topic
kafka.topic.partitions=3
kafka.topic.replication-factor=1
```

## 优势

### 1. 职责分离
- 每个配置类专注于特定功能
- 代码更清晰，易于理解
- 便于单独测试和维护

### 2. 配置隔离
- Producer和Consumer配置互不影响
- 可以独立调整各自的参数
- 减少配置冲突的可能性

### 3. 扩展性
- 新增Producer配置只需修改KafkaProducerConfig
- 新增Consumer配置只需修改KafkaConsumerConfig
- 便于添加新的配置类（如监控、安全等）

### 4. 维护性
- 配置变更影响范围明确
- 便于代码审查和版本控制
- 降低配置错误的风险

## 使用示例

### 注入Producer
```java
@Service
public class MarketDataProducer {
    @Autowired
    private KafkaTemplate<String, byte[]> kafkaTemplate; // 来自KafkaProducerConfig
    
    public void sendMessage(String key, byte[] data) {
        kafkaTemplate.send("market-data-topic", key, data);
    }
}
```

### 注入Consumer
```java
@Service
public class MarketDataConsumer {
    @KafkaListener(topics = "market-data-topic") // 使用KafkaConsumerConfig的配置
    public void consume(ConsumerRecord<String, byte[]> record) {
        // 处理消息
    }
}
```

## 配置文件组织

### 开发环境
```properties
# application-dev.properties
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.producer.batch-size=16384
spring.kafka.consumer.max-poll-records=100
```

### 生产环境
```properties
# application-prod.properties
spring.kafka.bootstrap-servers=prod-kafka-1:9092,prod-kafka-2:9092
spring.kafka.producer.batch-size=65536
spring.kafka.consumer.max-poll-records=1000
```

## 最佳实践

1. **配置验证**: 在每个配置类中添加参数验证
2. **日志记录**: 记录关键配置参数的加载情况
3. **文档维护**: 及时更新配置说明文档
4. **测试覆盖**: 为每个配置类编写单元测试
5. **监控告警**: 监控配置参数的运行时效果

## 迁移指南

如果从单一配置类迁移到分离配置：

1. **备份原配置**: 保存原有的配置文件
2. **逐步迁移**: 先迁移Producer，再迁移Consumer
3. **测试验证**: 每次迁移后进行功能测试
4. **性能对比**: 确保性能没有下降
5. **文档更新**: 更新相关文档和注释

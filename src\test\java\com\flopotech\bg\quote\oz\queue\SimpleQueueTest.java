package com.flopotech.bg.quote.oz.queue;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简化的队列测试 - 测试基本功能
 */
class SimpleQueueTest {

    private SymbolBasedMarketDataQueue queue;

    @BeforeEach
    void setUp() {
        queue = new SymbolBasedMarketDataQueue();
        
        // 设置测试用的symbol配置
        ReflectionTestUtils.setField(queue, "subscribeSymbols", "GBPUSD,EURUSD");
        
        // 模拟MarketDataProducer（设为null，测试时会处理）
        ReflectionTestUtils.setField(queue, "marketDataProducer", null);
        
        // 初始化队列
        queue.initialize();
    }

    @Test
    @DisplayName("测试队列初始化")
    void testQueueInitialization() {
        Set<String> configuredSymbols = queue.getConfiguredSymbols();
        
        assertEquals(2, configuredSymbols.size());
        assertTrue(configuredSymbols.contains("GBPUSD"));
        assertTrue(configuredSymbols.contains("EURUSD"));
        
        // 检查所有队列初始状态为空
        for (String symbol : configuredSymbols) {
            assertTrue(queue.isQueueEmpty(symbol));
        }
    }

    @Test
    @DisplayName("测试配置解析")
    void testConfigurationParsing() {
        // 测试空配置
        SymbolBasedMarketDataQueue emptyQueue = new SymbolBasedMarketDataQueue();
        ReflectionTestUtils.setField(emptyQueue, "subscribeSymbols", "");
        ReflectionTestUtils.setField(emptyQueue, "marketDataProducer", null);
        emptyQueue.initialize();
        
        assertTrue(emptyQueue.getConfiguredSymbols().isEmpty());
        
        // 测试多symbol配置
        SymbolBasedMarketDataQueue multiQueue = new SymbolBasedMarketDataQueue();
        ReflectionTestUtils.setField(multiQueue, "subscribeSymbols", "GBPUSD, EURUSD , USDJPY");
        ReflectionTestUtils.setField(multiQueue, "marketDataProducer", null);
        multiQueue.initialize();
        
        Set<String> symbols = multiQueue.getConfiguredSymbols();
        assertEquals(3, symbols.size());
        assertTrue(symbols.contains("GBPUSD"));
        assertTrue(symbols.contains("EURUSD"));
        assertTrue(symbols.contains("USDJPY"));
    }

    @Test
    @DisplayName("测试数据入队基本功能")
    void testBasicEnqueue() {
        // 创建测试数据
        MarketDataMsg.MarketData marketData = MarketDataMsg.MarketData.newBuilder()
                .setSymbol("GBPUSD")
                .setTimestamp(System.currentTimeMillis())
                .setBid("1.2500")
                .setOffer("1.2502")
                .build();

        // 测试入队（由于没有真实的producer，可能会失败，但不应该抛异常）
        assertDoesNotThrow(() -> {
            boolean result = queue.enqueue(marketData);
            // 结果可能是true或false，取决于NIO队列的内部实现
            System.out.println("入队结果: " + result);
        });
    }

    @Test
    @DisplayName("测试未配置symbol的处理")
    void testUnconfiguredSymbolHandling() {
        // 创建未配置symbol的测试数据
        MarketDataMsg.MarketData marketData = MarketDataMsg.MarketData.newBuilder()
                .setSymbol("UNKNOWN")
                .setTimestamp(System.currentTimeMillis())
                .setBid("1.0000")
                .setOffer("1.0002")
                .build();

        // 测试入队
        boolean result = queue.enqueue(marketData);
        assertFalse(result);
    }

    @Test
    @DisplayName("测试异常情况处理")
    void testExceptionHandling() {
        // 测试null数据
        assertFalse(queue.enqueue(null));
        
        // 测试空symbol
        MarketDataMsg.MarketData emptySymbol = MarketDataMsg.MarketData.newBuilder()
                .setSymbol("")
                .setTimestamp(System.currentTimeMillis())
                .setBid("1.0000")
                .setOffer("1.0002")
                .build();
        assertFalse(queue.enqueue(emptySymbol));
        
        // 测试未配置symbol的出队
        assertNull(queue.dequeue("UNKNOWN"));
    }

    @Test
    @DisplayName("测试队列状态监控")
    void testQueueStatusMonitoring() {
        // 获取队列信息
        String queueInfo = queue.getQueueInfo();
        
        assertNotNull(queueInfo);
        assertTrue(queueInfo.contains("NIO直接内存队列状态"));
        assertTrue(queueInfo.contains("双队列架构"));
        assertTrue(queueInfo.contains("零拷贝"));
        assertTrue(queueInfo.contains("GBPUSD"));
        assertTrue(queueInfo.contains("EURUSD"));
        
        System.out.println("队列状态信息:");
        System.out.println(queueInfo);
    }

    @Test
    @DisplayName("测试队列大小获取")
    void testQueueSizeRetrieval() {
        var queueSizes = queue.getQueueSizes();
        
        assertNotNull(queueSizes);
        assertEquals(2, queueSizes.size());
        assertTrue(queueSizes.containsKey("GBPUSD"));
        assertTrue(queueSizes.containsKey("EURUSD"));
        
        // 初始状态应该都是空队列
        assertEquals(0, queueSizes.get("GBPUSD"));
        assertEquals(0, queueSizes.get("EURUSD"));
    }

    @Test
    @DisplayName("测试队列关闭")
    void testQueueShutdown() {
        // 测试关闭不会抛异常
        assertDoesNotThrow(() -> {
            queue.shutdown();
        });
    }

    @Test
    @DisplayName("测试双队列架构设计验证")
    void testDualQueueArchitectureDesign() {
        // 验证队列信息包含双队列架构的关键信息
        String queueInfo = queue.getQueueInfo();
        
        // 验证包含双队列架构的关键词
        assertTrue(queueInfo.contains("双队列架构"));
        assertTrue(queueInfo.contains("rawQueue"));
        assertTrue(queueInfo.contains("sortedQueue"));
        assertTrue(queueInfo.contains("NIO直接内存"));
        assertTrue(queueInfo.contains("零拷贝"));
        
        System.out.println("=== 双队列架构验证 ===");
        System.out.println(queueInfo);
        
        // 验证配置的symbol都有对应的队列状态
        Set<String> configuredSymbols = queue.getConfiguredSymbols();
        for (String symbol : configuredSymbols) {
            assertTrue(queueInfo.contains(symbol));
            assertTrue(queue.isQueueEmpty(symbol)); // 初始状态应该为空
        }
    }
}

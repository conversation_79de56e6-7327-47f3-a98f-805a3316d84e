package com.flopotech.bg.quote.oz.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;

/**
 * BigDecimal工具类
 * 用于BigDecimal和String之间的转换，确保精度处理的一致性
 */
@Component
public class BigDecimalUtils {

    private static final Logger logger = LoggerFactory.getLogger(BigDecimalUtils.class);

    /**
     * 默认精度上下文 - 15位有效数字，HALF_UP舍入模式
     * 适用于金融市场数据的精度要求
     */
    public static final MathContext DEFAULT_MATH_CONTEXT = new MathContext(15, RoundingMode.HALF_UP);

    /**
     * 默认小数位数 - 用于价格显示
     */
    public static final int DEFAULT_SCALE = 8;

    /**
     * 将BigDecimal转换为字符串
     * 使用toPlainString()避免科学计数法
     *
     * @param value BigDecimal值
     * @return 字符串表示，如果输入为null则返回"0"
     */
    public static String toString(BigDecimal value) {
        if (value == null) {
            logger.warn("尝试转换null BigDecimal为字符串，返回默认值0");
            return "0";
        }
        return value.toPlainString();
    }

    /**
     * 将字符串转换为BigDecimal
     *
     * @param value 字符串值
     * @return BigDecimal对象，如果输入为null或空字符串则返回ZERO
     */
    public static BigDecimal fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            logger.warn("尝试转换null或空字符串为BigDecimal，返回ZERO");
            return BigDecimal.ZERO;
        }

        try {
            return new BigDecimal(value.trim(), DEFAULT_MATH_CONTEXT);
        } catch (NumberFormatException e) {
            logger.error("无法将字符串 '{}' 转换为BigDecimal: {}", value, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 将double转换为BigDecimal
     * 注意：这个方法主要用于从现有的double数据迁移
     *
     * @param value double值
     * @return BigDecimal对象
     */
    public static BigDecimal fromDouble(double value) {
        if (Double.isNaN(value) || Double.isInfinite(value)) {
            logger.warn("尝试转换无效的double值: {}, 返回ZERO", value);
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(value);
    }

    /**
     * 将BigDecimal转换为double
     * 注意：可能会丢失精度，仅在必要时使用
     *
     * @param value BigDecimal值
     * @return double值
     */
    public static double toDouble(BigDecimal value) {
        if (value == null) {
            logger.warn("尝试转换null BigDecimal为double，返回0.0");
            return 0.0;
        }
        return value.doubleValue();
    }

    /**
     * 安全的加法运算
     *
     * @param a 第一个操作数
     * @param b 第二个操作数
     * @return 相加结果
     */
    public static BigDecimal safeAdd(BigDecimal a, BigDecimal b) {
        if (a == null)
            a = BigDecimal.ZERO;
        if (b == null)
            b = BigDecimal.ZERO;
        return a.add(b, DEFAULT_MATH_CONTEXT);
    }

    /**
     * 安全的减法运算
     *
     * @param a 被减数
     * @param b 减数
     * @return 相减结果
     */
    public static BigDecimal safeSubtract(BigDecimal a, BigDecimal b) {
        if (a == null)
            a = BigDecimal.ZERO;
        if (b == null)
            b = BigDecimal.ZERO;
        return a.subtract(b, DEFAULT_MATH_CONTEXT);
    }

    /**
     * 安全的乘法运算
     *
     * @param a 第一个操作数
     * @param b 第二个操作数
     * @return 相乘结果
     */
    public static BigDecimal safeMultiply(BigDecimal a, BigDecimal b) {
        if (a == null)
            a = BigDecimal.ZERO;
        if (b == null)
            b = BigDecimal.ZERO;
        return a.multiply(b, DEFAULT_MATH_CONTEXT);
    }

    /**
     * 安全的除法运算
     *
     * @param a 被除数
     * @param b 除数
     * @return 相除结果
     */
    public static BigDecimal safeDivide(BigDecimal a, BigDecimal b) {
        if (a == null)
            a = BigDecimal.ZERO;
        if (b == null || b.compareTo(BigDecimal.ZERO) == 0) {
            logger.warn("尝试除以零或null，返回ZERO");
            return BigDecimal.ZERO;
        }
        return a.divide(b, DEFAULT_MATH_CONTEXT);
    }

    /**
     * 计算中间价 (bid + offer) / 2
     *
     * @param bid   买价
     * @param offer 卖价
     * @return 中间价
     */
    public static BigDecimal calculateMid(BigDecimal bid, BigDecimal offer) {
        BigDecimal sum = safeAdd(bid, offer);
        return safeDivide(sum, BigDecimal.valueOf(2));
    }

    /**
     * 验证BigDecimal是否有效（非null且为正数）
     *
     * @param value 要验证的值
     * @return 如果有效返回true
     */
    public static boolean isValidPrice(BigDecimal value) {
        return value != null && value.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 验证BigDecimal是否有效（非null且非负数）
     *
     * @param value 要验证的值
     * @return 如果有效返回true
     */
    public static boolean isValidSize(BigDecimal value) {
        return value != null && value.compareTo(BigDecimal.ZERO) >= 0;
    }

    /**
     * 格式化BigDecimal为指定小数位数的字符串
     *
     * @param value BigDecimal值
     * @param scale 小数位数
     * @return 格式化后的字符串
     */
    public static String formatToString(BigDecimal value, int scale) {
        if (value == null) {
            return "0";
        }
        return value.setScale(scale, RoundingMode.HALF_UP).toPlainString();
    }

    /**
     * 使用默认小数位数格式化BigDecimal
     *
     * @param value BigDecimal值
     * @return 格式化后的字符串
     */
    public static String formatToString(BigDecimal value) {
        return formatToString(value, DEFAULT_SCALE);
    }

    // ========== DepthData相关的便捷方法 ==========

    /**
     * 从DepthData中获取价格的BigDecimal值
     *
     * @param depthData DepthData对象
     * @return 价格的BigDecimal值
     */
    public static BigDecimal getPriceFromDepthData(com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData depthData) {
        if (depthData == null) {
            logger.warn("DepthData为null，返回ZERO价格");
            return BigDecimal.ZERO;
        }
        return fromString(depthData.getPx());
    }

    /**
     * 从DepthData中获取数量的BigDecimal值
     *
     * @param depthData DepthData对象
     * @return 数量的BigDecimal值
     */
    public static BigDecimal getSizeFromDepthData(com.flopotech.bg.quote.oz.msg.MarketDataMsg.DepthData depthData) {
        if (depthData == null) {
            logger.warn("DepthData为null，返回ZERO数量");
            return BigDecimal.ZERO;
        }
        return fromString(depthData.getSz());
    }

    /**
     * 从MarketData中获取bid价格的BigDecimal值
     *
     * @param marketData MarketData对象
     * @return bid价格的BigDecimal值
     */
    public static BigDecimal getBidFromMarketData(com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData marketData) {
        if (marketData == null) {
            logger.warn("MarketData为null，返回ZERO bid价格");
            return BigDecimal.ZERO;
        }
        return fromString(marketData.getBid());
    }

    /**
     * 从MarketData中获取offer价格的BigDecimal值
     *
     * @param marketData MarketData对象
     * @return offer价格的BigDecimal值
     */
    public static BigDecimal getOfferFromMarketData(com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData marketData) {
        if (marketData == null) {
            logger.warn("MarketData为null，返回ZERO offer价格");
            return BigDecimal.ZERO;
        }
        return fromString(marketData.getOffer());
    }

    /**
     * 从MarketData中获取mid价格的BigDecimal值
     *
     * @param marketData MarketData对象
     * @return mid价格的BigDecimal值
     */
    public static BigDecimal getMidFromMarketData(com.flopotech.bg.quote.oz.msg.MarketDataMsg.MarketData marketData) {
        if (marketData == null) {
            logger.warn("MarketData为null，返回ZERO mid价格");
            return BigDecimal.ZERO;
        }
        return fromString(marketData.getMid());
    }
}

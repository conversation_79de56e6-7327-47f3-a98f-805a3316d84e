package com.flopotech.bg.quote.oz.queue;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import com.flopotech.bg.quote.oz.service.MarketDataProducer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SymbolBasedMarketDataQueue 测试类
 */
class SymbolBasedMarketDataQueueTest {

    private SymbolBasedMarketDataQueue queue;
    private TestMarketDataProducer testProducer;

    @BeforeEach
    void setUp() {
        queue = new SymbolBasedMarketDataQueue();
        testProducer = new TestMarketDataProducer();

        // 注入测试用的producer
        ReflectionTestUtils.setField(queue, "marketDataProducer", testProducer);

        // 设置测试用的symbol配置
        ReflectionTestUtils.setField(queue, "subscribeSymbols", "GBPUSD,EURUSD,USDJPY");

        // 初始化队列
        queue.initialize();
    }

    /**
     * 测试用的MarketDataProducer实现
     */
    private static class TestMarketDataProducer extends MarketDataProducer {
        private int sendCount = 0;

        @Override
        public void sendMarketData(MarketDataMsg.MarketData marketData) {
            sendCount++;
            // 不实际发送，只计数
        }

        public int getSendCount() {
            return sendCount;
        }

        public void resetCount() {
            sendCount = 0;
        }
    }

    @Test
    @DisplayName("测试队列初始化")
    void testInitialization() {
        Set<String> configuredSymbols = queue.getConfiguredSymbols();

        assertEquals(3, configuredSymbols.size());
        assertTrue(configuredSymbols.contains("GBPUSD"));
        assertTrue(configuredSymbols.contains("EURUSD"));
        assertTrue(configuredSymbols.contains("USDJPY"));
    }

    @Test
    @DisplayName("测试配置symbol解析")
    void testSymbolParsing() {
        // 测试不同的配置格式
        SymbolBasedMarketDataQueue testQueue = new SymbolBasedMarketDataQueue();
        TestMarketDataProducer testProducer2 = new TestMarketDataProducer();

        // 测试带空格的配置
        ReflectionTestUtils.setField(testQueue, "subscribeSymbols", " GBPUSD , EURUSD , USDJPY ");
        ReflectionTestUtils.setField(testQueue, "marketDataProducer", testProducer2);
        testQueue.initialize();

        Set<String> symbols = testQueue.getConfiguredSymbols();
        assertEquals(3, symbols.size());
        assertTrue(symbols.contains("GBPUSD"));
        assertTrue(symbols.contains("EURUSD"));
        assertTrue(symbols.contains("USDJPY"));
    }

    @Test
    @DisplayName("测试空配置处理")
    void testEmptyConfiguration() {
        SymbolBasedMarketDataQueue testQueue = new SymbolBasedMarketDataQueue();
        TestMarketDataProducer testProducer2 = new TestMarketDataProducer();
        ReflectionTestUtils.setField(testQueue, "subscribeSymbols", "");
        ReflectionTestUtils.setField(testQueue, "marketDataProducer", testProducer2);
        testQueue.initialize();

        Set<String> symbols = testQueue.getConfiguredSymbols();
        assertTrue(symbols.isEmpty());
    }

    @Test
    @DisplayName("测试市场数据入队")
    void testEnqueueMarketData() {
        // 创建测试数据
        MarketDataMsg.MarketData marketData = MarketDataMsg.MarketData.newBuilder()
                .setSymbol("GBPUSD")
                .setTimestamp(System.currentTimeMillis())
                .setBid("1.2500")
                .setOffer("1.2502")
                .build();

        // 测试入队
        boolean result = queue.enqueue(marketData);
        assertTrue(result);

        // 检查队列状态
        Map<String, Integer> queueSizes = queue.getQueueSizes();
        assertEquals(1, queueSizes.get("GBPUSD").intValue());
    }

    @Test
    @DisplayName("测试未配置symbol的处理")
    void testUnconfiguredSymbol() {
        // 创建未配置symbol的测试数据
        MarketDataMsg.MarketData marketData = MarketDataMsg.MarketData.newBuilder()
                .setSymbol("UNKNOWN")
                .setTimestamp(System.currentTimeMillis())
                .setBid("1.0000")
                .setOffer("1.0002")
                .build();

        // 测试入队
        boolean result = queue.enqueue(marketData);
        assertFalse(result);

        // 检查队列状态
        Map<String, Integer> queueSizes = queue.getQueueSizes();
        assertFalse(queueSizes.containsKey("UNKNOWN"));
    }

    @Test
    @DisplayName("测试null数据处理")
    void testNullData() {
        boolean result = queue.enqueue(null);
        assertFalse(result);
    }

    @Test
    @DisplayName("测试空symbol处理")
    void testEmptySymbol() {
        MarketDataMsg.MarketData marketData = MarketDataMsg.MarketData.newBuilder()
                .setSymbol("")
                .setTimestamp(System.currentTimeMillis())
                .setBid("1.0000")
                .setOffer("1.0002")
                .build();

        boolean result = queue.enqueue(marketData);
        assertFalse(result);
    }

    @Test
    @DisplayName("测试队列状态检查")
    void testQueueStatusCheck() {
        // 初始状态应该为空
        assertTrue(queue.isQueueEmpty("GBPUSD"));
        assertTrue(queue.isQueueEmpty("EURUSD"));
        assertTrue(queue.isQueueEmpty("USDJPY"));

        // 添加数据后检查
        MarketDataMsg.MarketData marketData = MarketDataMsg.MarketData.newBuilder()
                .setSymbol("GBPUSD")
                .setTimestamp(System.currentTimeMillis())
                .setBid("1.2500")
                .setOffer("1.2502")
                .build();

        queue.enqueue(marketData);
        assertFalse(queue.isQueueEmpty("GBPUSD"));
        assertTrue(queue.isQueueEmpty("EURUSD"));
    }

    @Test
    @DisplayName("测试批量数据处理")
    void testBatchProcessing() throws InterruptedException {
        // 添加多个数据
        for (int i = 0; i < 5; i++) {
            MarketDataMsg.MarketData marketData = MarketDataMsg.MarketData.newBuilder()
                    .setSymbol("GBPUSD")
                    .setTimestamp(System.currentTimeMillis() + i)
                    .setBid("1.250" + i)
                    .setOffer("1.250" + (i + 2))
                    .build();
            queue.enqueue(marketData);
        }

        // 等待一段时间让队列处理
        Thread.sleep(200);

        // 验证producer被调用
        verify(marketDataProducer, atLeast(1)).sendMarketData(any(MarketDataMsg.MarketData.class));
    }

    @Test
    @DisplayName("测试多symbol并发处理")
    void testMultiSymbolProcessing() throws InterruptedException {
        String[] symbols = { "GBPUSD", "EURUSD", "USDJPY" };

        // 为每个symbol添加数据
        for (String symbol : symbols) {
            for (int i = 0; i < 3; i++) {
                MarketDataMsg.MarketData marketData = MarketDataMsg.MarketData.newBuilder()
                        .setSymbol(symbol)
                        .setTimestamp(System.currentTimeMillis() + i)
                        .setBid("1.000" + i)
                        .setOffer("1.000" + (i + 2))
                        .build();
                queue.enqueue(marketData);
            }
        }

        // 等待处理
        Thread.sleep(500);

        // 验证所有数据都被处理
        verify(marketDataProducer, atLeast(9)).sendMarketData(any(MarketDataMsg.MarketData.class));
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty name="logstashAddress" source="logstash.address" defaultValue="localhost:5044"/>
    <springProperty name="appName" source="spring.application.name" defaultValue="app"/>

    <!-- 控制台输出 -->
    <appender name="JSON_CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <fieldNames>
                <timestamp>@timestamp</timestamp>
                <level>log.level</level>
                <thread>thread.name</thread>
                <logger>log.logger</logger>
                <message>message</message>
                <stackTrace>error.stackTrace</stackTrace> <!-- 大小写修正 -->
            </fieldNames>
            <!-- 方法/行号按需启用 -->
            <provider class="net.logstash.logback.composite.loggingevent.LoggingEventPatternJsonProvider">
                <pattern>{ "line": "%line", "method": "%method" }</pattern>
            </provider>
        </encoder>
    </appender>

    <!-- Logstash 输出 -->
    <appender name="LOGSTASH" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
        <destination>${logstashAddress}</destination>
        <ringBufferSize>1024</ringBufferSize> <!-- 使用 queueSize 替代 ringBufferSize -->
        <writeBufferSize>32768</writeBufferSize>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <fieldNames>
                <stackTrace>error.stackTrace</stackTrace> <!-- 大小写修正 -->
            </fieldNames>
            <!-- 修复JSON语法 -->
            <customFields>{ "appname": "${appName}" }</customFields>

            <provider class="net.logstash.logback.composite.loggingevent.LoggingEventPatternJsonProvider">
                <pattern>{ "line": "%line", "method": "%method" }</pattern>
            </provider>
        </encoder>
    </appender>

    <!-- 异步日志 -->
    <appender name="ASYNC_LOGSTASH" class="ch.qos.logback.classic.AsyncAppender">
<!--        <ringBufferSize>1024</ringBufferSize>-->
<!--        <writeBufferSize>32768</writeBufferSize>-->
        <appender-ref ref="LOGSTASH"/>
    </appender>



    <root level="INFO">
        <appender-ref ref="JSON_CONSOLE"/>
        <appender-ref ref="ASYNC_LOGSTASH"/>
    </root>
</configuration>
syntax = "proto3";

package com.flopotech.bg.quote.oz.msg;

// OZ深度数据条目
message DepthData {
    // 价格类型: 0-bid, 1-offer
    int32 tp = 1;

    // 价格 - 使用字符串存储BigDecimal以保证精度
    string px = 2;

    // 可交易量 - 使用字符串存储BigDecimal以保证精度
    string sz = 3;

    // 报价条件: A = Tradeable Quote(可交易报价), I = Indicative Quote(参考报价)
    string condition = 4;

    // 发起者
    string ori = 5;

    // 报价单唯一标识
    string uqId = 6;
}

// OZ报价消息
message MarketData {
    // 时间戳 - 接收到OZ那边的发送时间，在fix标准请求头中
    int64 timestamp = 1;

    // bg-quote接收的时间 , 接收到行情到本地的时间
    int64 recvTime = 2;

    //是否为超时数据 , 行情数据是否为旧数据 1-是 0-否
    int32 oldData = 3;

    // 合约
    string symbol = 4;

    // 当前行情OZ的最优bid价 - OZ返回的MDEntryType=0，使用字符串存储BigDecimal以保证精度
    string bid = 5;

    // 当前行情OZ的最优offer价 - OZ返回的MDEntryType=1，使用字符串存储BigDecimal以保证精度
    string offer = 6;

    // 中间价 - (bid+offer)/2，使用字符串存储BigDecimal以保证精度
    string mid = 7;

    // OZ的深度数据集合
    repeated DepthData data = 8;
}
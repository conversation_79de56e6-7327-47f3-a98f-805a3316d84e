
# MQ消息设计规范(Ka<PERSON>ka)

## 消息规范性说明

- **消息中间件**: KafkaMQ
- **序列化类型**: Protobuf
- **protobuf生成msg文件的命令**:mvn protobuf:compile
## Topic设计

### 行情类（quote）

| 序号 | Topic名称           | 生产者 | 消费者         | 用途说明 |
|------|-------------------|--------|-------------|----------|
| 1 | qot-quote.oz      | 行情端口(oz)bg-oz-quote | 行情引擎(eg-qe) | |

## 行情消息格式

### 1. 推送原始行情

- **MQ消息类型**: 发布订阅
- **Topic**: qot-quote.oz

#### 请求数据格式
| 字段         | 中文名             | 类型     | 说明                                                    |
|------------|-----------------|--------|-------------------------------------------------------|
| timestamp  | 时间戳             | Long   | 接收到OZ那边的发送时间，在fix标准请求头中                               |
| recvTime   | bg-quote接收的时间   | Long   | 接收到行情到本地的时间                                           |
| oldData    | 是否为超时数据         | int    | 行情数据是否为旧数据 1-是 0-否                                    |
| symbol     | 合约              | String | -                                                     |
| bid        | 当前行情OZ的最优bid价   | double | 这里的bid价指的是OZ返回的MDEntryType=0                          |
| offer      | 当前行情OZ的最优offer价 | double | 这里的offer价指的是OZ返回的MDEntryType=1                        |
| mid        | 中间价             | double | (bid+offer)/2                                         |
| data       | OZ的深度数据集合       | Array  | -                                                     |
| >tp        | 价格类型            | int    | 0-bid 1=offer                                         |
| >px        | 价格              | double | -                                                     |
| >sz        | 可交易量            | double | -                                                     |
| >condition | 报价条件            | string | A = Tradeable Quote(可交易报价) I = Indicative Quote(参考报价) |
| >ori       | 发起者             | string | -                                                     |
| >uqId      | 报价单唯一标识         | string | 如果后续更新某一条报价记录时候需要用到                                   |

package com.flopotech.bg.quote.oz.service;


import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import quickfix.SessionID;

import java.time.LocalDateTime;

@Component
public class SubscriptionSystem {

    @Autowired
    private ApplicationContext context;

    private static final Logger logger = LoggerFactory.getLogger(SubscriptionSystem.class);
    private static final SessionID SESSION_ID = new SessionID("FIX.4.4", "SENDER", "TARGET");
    @Autowired
    private SubscriptionService subscriptionService;

    @PostConstruct
    public void init() {
        try {
            logger.info("Initializing SubscriptionSystem");

            // 1. 初始化订阅
//            subscriptionService.subscribeAll(SESSION_ID);

            // 3. 添加定期会话检查


        } catch (Exception e) {
            logger.error("Subscription system initialization failed", e);
        }
    }
}
package com.flopotech.bg.quote.oz.queue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

/**
 * 队列监控服务
 * 定期监控队列状态，帮助调试分片问题
 */
@Service
public class QueueMonitorService {

    private static final Logger logger = LoggerFactory.getLogger(QueueMonitorService.class);

    @Autowired
    private SymbolBasedMarketDataQueue symbolBasedQueue;

    /**
     * 每30秒监控一次队列状态
     */
    @Scheduled(fixedRate = 30000)
    public void monitorQueueStatus() {
        try {
            // 获取配置的symbols
            Set<String> configuredSymbols = symbolBasedQueue.getConfiguredSymbols();
            
            // 获取队列大小
            Map<String, Integer> queueSizes = symbolBasedQueue.getQueueSizes();
            
            logger.info("=== 队列状态监控 ===");
            logger.info("配置的symbols: {}", configuredSymbols);
            logger.info("队列状态: {}", queueSizes);
            
            // 检查是否有积压
            boolean hasBacklog = false;
            for (Map.Entry<String, Integer> entry : queueSizes.entrySet()) {
                if (entry.getValue() > 100) {
                    logger.warn("symbol {} 队列积压: {} 条消息", entry.getKey(), entry.getValue());
                    hasBacklog = true;
                }
            }
            
            if (!hasBacklog && !queueSizes.isEmpty()) {
                logger.debug("所有队列运行正常");
            }
            
            // 检查是否有配置的symbol没有队列
            for (String symbol : configuredSymbols) {
                if (!queueSizes.containsKey(symbol)) {
                    logger.warn("配置的symbol {} 没有对应的队列", symbol);
                }
            }
            
        } catch (Exception e) {
            logger.error("监控队列状态时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 手动检查队列状态
     */
    public void checkQueueStatus() {
        monitorQueueStatus();
    }

    /**
     * 获取详细的队列信息
     */
    public String getDetailedQueueInfo() {
        StringBuilder info = new StringBuilder();
        
        Set<String> configuredSymbols = symbolBasedQueue.getConfiguredSymbols();
        Map<String, Integer> queueSizes = symbolBasedQueue.getQueueSizes();
        
        info.append("=== 详细队列信息 ===\n");
        info.append("配置的symbols数量: ").append(configuredSymbols.size()).append("\n");
        info.append("活跃队列数量: ").append(queueSizes.size()).append("\n");
        
        info.append("\n配置的symbols:\n");
        for (String symbol : configuredSymbols) {
            Integer queueSize = queueSizes.get(symbol);
            info.append("  ").append(symbol).append(": ");
            if (queueSize != null) {
                info.append(queueSize).append(" 条消息");
            } else {
                info.append("无队列");
            }
            info.append("\n");
        }
        
        return info.toString();
    }
}

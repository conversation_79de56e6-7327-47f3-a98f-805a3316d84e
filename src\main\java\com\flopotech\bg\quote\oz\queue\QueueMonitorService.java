package com.flopotech.bg.quote.oz.queue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

/**
 * 简化的队列监控服务
 * 直接监控NioDirectMemoryQueue状态
 */
@Service
public class QueueMonitorService {

    private static final Logger logger = LoggerFactory.getLogger(QueueMonitorService.class);

    @Autowired
    private NioDirectMemoryQueue<String> nioQueue;

    /**
     * 从配置文件读取订阅的symbol列表
     */
    @Value("${quickfix.onezero.subscribe.symbol}")
    private String subscribeSymbols;

    /**
     * 每30秒监控一次队列状态
     */
    @Scheduled(fixedRate = 30000)
    public void monitorQueueStatus() {
        try {
            // 解析配置的symbols
            Set<String> configuredSymbols = parseConfiguredSymbols();

            logger.info("=== NIO队列状态监控 ===");
            logger.info("配置的symbols: {}", configuredSymbols);

            // 检查每个symbol的队列状态
            for (String symbol : configuredSymbols) {
                boolean isEmpty = nioQueue.isEmpty(symbol);
                if (isEmpty) {
                    logger.debug("symbol {} 队列为空", symbol);
                } else {
                    logger.info("symbol {} 队列有数据", symbol);
                }
            }

            logger.debug("NIO队列监控完成");

        } catch (Exception e) {
            logger.error("队列状态监控失败", e);
        }
    }

    /**
     * 解析配置文件中的symbol列表
     */
    private Set<String> parseConfiguredSymbols() {
        if (subscribeSymbols == null || subscribeSymbols.trim().isEmpty()) {
            return new HashSet<>();
        }

        Set<String> symbols = new HashSet<>();
        String[] symbolArray = subscribeSymbols.split(",");
        for (String symbol : symbolArray) {
            String trimmedSymbol = symbol.trim();
            if (!trimmedSymbol.isEmpty()) {
                symbols.add(trimmedSymbol);
            }
        }
        return symbols;
    }

    /**
     * 手动检查队列状态
     */
    public void checkQueueStatus() {
        monitorQueueStatus();
    }

    /**
     * 获取详细的队列信息
     */
    public String getDetailedQueueInfo() {
        StringBuilder info = new StringBuilder();

        Set<String> configuredSymbols = parseConfiguredSymbols();

        info.append("=== 简化NIO队列信息 ===\n");
        info.append("配置的symbols数量: ").append(configuredSymbols.size()).append("\n");
        info.append("配置的symbols: ").append(configuredSymbols).append("\n\n");

        info.append("队列状态详情:\n");
        for (String symbol : configuredSymbols) {
            boolean isEmpty = nioQueue.isEmpty(symbol);
            info.append("  ").append(symbol).append(": ").append(isEmpty ? "空队列" : "有数据").append("\n");
        }

        return info.toString();
    }
}
